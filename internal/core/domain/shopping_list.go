package domain

import (
	"time"

	"github.com/google/uuid"
)

// ShoppingListStatus represents the status of a shopping list
type ShoppingListStatus string

const (
	ShoppingListStatusActive    ShoppingListStatus = "active"
	ShoppingListStatusCompleted ShoppingListStatus = "completed"
	ShoppingListStatusArchived  ShoppingListStatus = "archived"
)

// ShoppingList represents a shopping list entity
type ShoppingList struct {
	ID          uuid.UUID          `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	PantryID    uuid.UUID          `json:"pantry_id" gorm:"type:uuid;not null;column:pantry_id"`
	Name        string             `json:"name" gorm:"type:varchar(255);not null"`
	Description *string            `json:"description,omitempty" gorm:"type:text"`
	CreatedBy   uuid.UUID          `json:"created_by" gorm:"type:uuid;not null;column:created_by"`
	Status      ShoppingListStatus `json:"status" gorm:"type:varchar(20);not null;default:'active'"`
	CreatedAt   time.Time          `json:"created_at" gorm:"type:timestamp with time zone;not null;default:NOW()"`
	UpdatedAt   time.Time          `json:"updated_at" gorm:"type:timestamp with time zone;not null;default:NOW()"`

	// Enhanced Shopping List Features
	ReceiptImageURL *string    `json:"receipt_image_url,omitempty" gorm:"type:varchar(500);column:receipt_image_url"`
	StoreName       *string    `json:"store_name,omitempty" gorm:"type:varchar(255);column:store_name"`
	StoreAddress    *string    `json:"store_address,omitempty" gorm:"type:varchar(500);column:store_address"`
	StoreContact    *string    `json:"store_contact,omitempty" gorm:"type:varchar(255);column:store_contact"`
	TotalAmount     *float64   `json:"total_amount,omitempty" gorm:"type:decimal(10,2);column:total_amount"`
	CompletedAt     *time.Time `json:"completed_at,omitempty" gorm:"type:timestamp with time zone;column:completed_at"`

	// Relationships
	Items []ShoppingListItemEntity `json:"items,omitempty" gorm:"foreignKey:ShoppingListID"`

	// Domain events
	events []DomainEvent `json:"-" gorm:"-"`
}

// TableName returns the table name for GORM
func (ShoppingList) TableName() string {
	return "shopping_lists"
}

// ShoppingListItemEntity represents an item in a shopping list
type ShoppingListItemEntity struct {
	ID               uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ShoppingListID   uuid.UUID  `json:"shopping_list_id" gorm:"type:uuid;not null;column:shopping_list_id"`
	ProductVariantID *uuid.UUID `json:"product_variant_id,omitempty" gorm:"type:uuid;column:product_variant_id"`
	FreeTextName     *string    `json:"free_text_name,omitempty" gorm:"type:varchar(255);column:free_text_name"`
	QuantityDesired  float64    `json:"quantity_desired" gorm:"type:decimal(10,3);not null;column:quantity_desired"`
	UnitOfMeasureID  *uuid.UUID `json:"unit_of_measure_id,omitempty" gorm:"type:uuid;column:unit_of_measure_id"`
	Notes            *string    `json:"notes,omitempty" gorm:"type:text"`
	IsPurchased      bool       `json:"is_purchased" gorm:"type:boolean;not null;default:false;column:is_purchased"`
	PurchasedAt      *time.Time `json:"purchased_at,omitempty" gorm:"type:timestamp with time zone;column:purchased_at"`
	CreatedAt        time.Time  `json:"created_at" gorm:"type:timestamp with time zone;not null;default:NOW()"`
	UpdatedAt        time.Time  `json:"updated_at" gorm:"type:timestamp with time zone;not null;default:NOW()"`

	// Enhanced Price Tracking Features
	EstimatedPrice *float64 `json:"estimated_price,omitempty" gorm:"type:decimal(10,2);column:estimated_price"`
	ActualPrice    *float64 `json:"actual_price,omitempty" gorm:"type:decimal(10,2);column:actual_price"`
	PricePerUnit   *float64 `json:"price_per_unit,omitempty" gorm:"type:decimal(10,2);column:price_per_unit"`

	// Domain events
	events []DomainEvent `json:"-" gorm:"-"`
}

// TableName returns the table name for GORM
func (ShoppingListItemEntity) TableName() string {
	return "shopping_list_items"
}

// NewShoppingList creates a new shopping list
func NewShoppingList(pantryID uuid.UUID, name string, description *string, createdBy uuid.UUID) *ShoppingList {
	now := time.Now()

	list := &ShoppingList{
		ID:          uuid.New(),
		PantryID:    pantryID,
		Name:        name,
		Description: description,
		CreatedBy:   createdBy,
		Status:      ShoppingListStatusActive,
		CreatedAt:   now,
		UpdatedAt:   now,
		Items:       make([]ShoppingListItemEntity, 0),
		events:      make([]DomainEvent, 0),
	}

	// Add domain event
	list.AddEvent(ShoppingListCreatedEvent{
		ShoppingListID: list.ID,
		PantryID:       list.PantryID,
		Name:           list.Name,
		CreatedBy:      list.CreatedBy,
		CreatedAt:      list.CreatedAt,
	})

	return list
}

// NewShoppingListItem creates a new shopping list item
func NewShoppingListItem(
	shoppingListID uuid.UUID,
	productVariantID *uuid.UUID,
	freeTextName *string,
	quantityDesired float64,
	unitOfMeasureID *uuid.UUID,
	notes *string,
) *ShoppingListItemEntity {
	now := time.Now()

	item := &ShoppingListItemEntity{
		ID:               uuid.New(),
		ShoppingListID:   shoppingListID,
		ProductVariantID: productVariantID,
		FreeTextName:     freeTextName,
		QuantityDesired:  quantityDesired,
		UnitOfMeasureID:  unitOfMeasureID,
		Notes:            notes,
		IsPurchased:      false,
		CreatedAt:        now,
		UpdatedAt:        now,
		events:           make([]DomainEvent, 0),
	}

	// Add domain event
	item.AddEvent(ShoppingListItemAddedEvent{
		ShoppingListItemID: item.ID,
		ShoppingListID:     item.ShoppingListID,
		ProductVariantID:   item.ProductVariantID,
		FreeTextName:       item.FreeTextName,
		QuantityDesired:    item.QuantityDesired,
		CreatedAt:          item.CreatedAt,
	})

	return item
}

// Update updates the shopping list details
func (sl *ShoppingList) Update(name string, description *string) {
	sl.Name = name
	sl.Description = description
	sl.UpdatedAt = time.Now()

	// Add domain event
	sl.AddEvent(ShoppingListUpdatedEvent{
		ShoppingListID: sl.ID,
		Name:           sl.Name,
		Description:    sl.Description,
		UpdatedAt:      sl.UpdatedAt,
	})
}

// ChangeStatus changes the status of the shopping list
func (sl *ShoppingList) ChangeStatus(status ShoppingListStatus) {
	oldStatus := sl.Status
	sl.Status = status
	sl.UpdatedAt = time.Now()

	// Add domain event
	sl.AddEvent(ShoppingListStatusChangedEvent{
		ShoppingListID: sl.ID,
		OldStatus:      oldStatus,
		NewStatus:      sl.Status,
		UpdatedAt:      sl.UpdatedAt,
	})
}

// MarkAsCompleted marks the shopping list as completed
func (sl *ShoppingList) MarkAsCompleted() {
	sl.ChangeStatus(ShoppingListStatusCompleted)
}

// Archive archives the shopping list
func (sl *ShoppingList) Archive() {
	sl.ChangeStatus(ShoppingListStatusArchived)
}

// Activate activates the shopping list
func (sl *ShoppingList) Activate() {
	sl.ChangeStatus(ShoppingListStatusActive)
}

// CompleteShoppingList marks the shopping list as completed with store and receipt information
func (sl *ShoppingList) CompleteShoppingList(storeName, storeAddress, storeContact *string, receiptImageURL *string, totalAmount *float64) {
	now := time.Now()
	sl.Status = ShoppingListStatusCompleted
	sl.CompletedAt = &now
	sl.StoreName = storeName
	sl.StoreAddress = storeAddress
	sl.StoreContact = storeContact
	sl.ReceiptImageURL = receiptImageURL
	sl.TotalAmount = totalAmount
	sl.UpdatedAt = now

	// Add domain event
	sl.AddEvent(ShoppingListCompletedEvent{
		ShoppingListID:  sl.ID,
		PantryID:        sl.PantryID,
		CompletedAt:     *sl.CompletedAt,
		StoreName:       sl.StoreName,
		StoreAddress:    sl.StoreAddress,
		TotalAmount:     sl.TotalAmount,
		ReceiptImageURL: sl.ReceiptImageURL,
	})
}

// UpdateStoreInformation updates store information for the shopping list
func (sl *ShoppingList) UpdateStoreInformation(storeName, storeAddress, storeContact *string) {
	sl.StoreName = storeName
	sl.StoreAddress = storeAddress
	sl.StoreContact = storeContact
	sl.UpdatedAt = time.Now()

	// Add domain event
	sl.AddEvent(ShoppingListStoreUpdatedEvent{
		ShoppingListID: sl.ID,
		PantryID:       sl.PantryID,
		StoreName:      sl.StoreName,
		StoreAddress:   sl.StoreAddress,
		StoreContact:   sl.StoreContact,
		UpdatedAt:      sl.UpdatedAt,
	})
}

// UpdateItem updates a shopping list item
func (sli *ShoppingListItemEntity) UpdateItem(
	productVariantID *uuid.UUID,
	freeTextName *string,
	quantityDesired float64,
	unitOfMeasureID *uuid.UUID,
	notes *string,
) {
	sli.ProductVariantID = productVariantID
	sli.FreeTextName = freeTextName
	sli.QuantityDesired = quantityDesired
	sli.UnitOfMeasureID = unitOfMeasureID
	sli.Notes = notes
	sli.UpdatedAt = time.Now()

	// Add domain event
	sli.AddEvent(ShoppingListItemUpdatedEvent{
		ShoppingListItemID: sli.ID,
		ShoppingListID:     sli.ShoppingListID,
		ProductVariantID:   sli.ProductVariantID,
		FreeTextName:       sli.FreeTextName,
		QuantityDesired:    sli.QuantityDesired,
		UpdatedAt:          sli.UpdatedAt,
	})
}

// MarkAsPurchased marks the item as purchased
func (sli *ShoppingListItemEntity) MarkAsPurchased() {
	if !sli.IsPurchased {
		sli.IsPurchased = true
		now := time.Now()
		sli.PurchasedAt = &now
		sli.UpdatedAt = now

		// Add domain event
		sli.AddEvent(ShoppingListItemPurchasedEvent{
			ShoppingListItemID: sli.ID,
			ShoppingListID:     sli.ShoppingListID,
			ProductVariantID:   sli.ProductVariantID,
			FreeTextName:       sli.FreeTextName,
			QuantityDesired:    sli.QuantityDesired,
			PurchasedAt:        *sli.PurchasedAt,
		})
	}
}

// MarkAsNotPurchased marks the item as not purchased
func (sli *ShoppingListItemEntity) MarkAsNotPurchased() {
	if sli.IsPurchased {
		sli.IsPurchased = false
		sli.PurchasedAt = nil
		sli.ActualPrice = nil
		sli.PricePerUnit = nil
		sli.UpdatedAt = time.Now()

		// Add domain event
		sli.AddEvent(ShoppingListItemUnpurchasedEvent{
			ShoppingListItemID: sli.ID,
			ShoppingListID:     sli.ShoppingListID,
			UpdatedAt:          sli.UpdatedAt,
		})
	}
}

// UpdatePriceInformation updates price information for the shopping list item
func (sli *ShoppingListItemEntity) UpdatePriceInformation(estimatedPrice, actualPrice, pricePerUnit *float64) {
	sli.EstimatedPrice = estimatedPrice
	sli.ActualPrice = actualPrice
	sli.PricePerUnit = pricePerUnit
	sli.UpdatedAt = time.Now()

	// Add domain event
	sli.AddEvent(ShoppingListItemPriceUpdatedEvent{
		ShoppingListItemID: sli.ID,
		ShoppingListID:     sli.ShoppingListID,
		EstimatedPrice:     sli.EstimatedPrice,
		ActualPrice:        sli.ActualPrice,
		PricePerUnit:       sli.PricePerUnit,
		UpdatedAt:          sli.UpdatedAt,
	})
}

// MarkAsPurchasedWithPrice marks the item as purchased with price information
func (sli *ShoppingListItemEntity) MarkAsPurchasedWithPrice(actualPrice, pricePerUnit *float64) {
	if !sli.IsPurchased {
		sli.IsPurchased = true
		now := time.Now()
		sli.PurchasedAt = &now
		sli.ActualPrice = actualPrice
		sli.PricePerUnit = pricePerUnit
		sli.UpdatedAt = now

		// Add domain event
		sli.AddEvent(ShoppingListItemPurchasedEvent{
			ShoppingListItemID: sli.ID,
			ShoppingListID:     sli.ShoppingListID,
			ProductVariantID:   sli.ProductVariantID,
			FreeTextName:       sli.FreeTextName,
			QuantityDesired:    sli.QuantityDesired,
			PurchasedAt:        *sli.PurchasedAt,
		})
	}
}

// Domain event methods for ShoppingList
func (sl *ShoppingList) AddEvent(event DomainEvent) {
	sl.events = append(sl.events, event)
}

func (sl *ShoppingList) GetEvents() []DomainEvent {
	return sl.events
}

func (sl *ShoppingList) ClearEvents() {
	sl.events = make([]DomainEvent, 0)
}

// Domain event methods for ShoppingListItemEntity
func (sli *ShoppingListItemEntity) AddEvent(event DomainEvent) {
	sli.events = append(sli.events, event)
}

func (sli *ShoppingListItemEntity) GetEvents() []DomainEvent {
	return sli.events
}

func (sli *ShoppingListItemEntity) ClearEvents() {
	sli.events = make([]DomainEvent, 0)
}

// Validation methods
func (sl *ShoppingList) IsValid() bool {
	return sl.Name != "" && sl.PantryID != uuid.Nil && sl.CreatedBy != uuid.Nil
}

func (sli *ShoppingListItemEntity) IsValid() bool {
	return sli.ShoppingListID != uuid.Nil &&
		sli.QuantityDesired > 0 &&
		(sli.ProductVariantID != nil || sli.FreeTextName != nil)
}

// Helper methods
func (sl *ShoppingList) GetPurchasedItemsCount() int {
	count := 0
	for _, item := range sl.Items {
		if item.IsPurchased {
			count++
		}
	}
	return count
}

func (sl *ShoppingList) GetTotalItemsCount() int {
	return len(sl.Items)
}

func (sl *ShoppingList) IsCompleted() bool {
	if len(sl.Items) == 0 {
		return false
	}
	return sl.GetPurchasedItemsCount() == sl.GetTotalItemsCount()
}

func (sl *ShoppingList) GetCompletionPercentage() float64 {
	if len(sl.Items) == 0 {
		return 0.0
	}
	return float64(sl.GetPurchasedItemsCount()) / float64(sl.GetTotalItemsCount()) * 100.0
}
