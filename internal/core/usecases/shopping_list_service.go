package usecases

import (
	"context"
	"fmt"

	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// ShoppingListService implements the shopping list business logic
type ShoppingListService struct {
	shoppingListRepo domain.ShoppingListRepository
	pantryRepo       domain.PantryRepository
	userRepo         domain.UserRepository
	authService      domain.PantryAuthorizationService
}

// NewShoppingListService creates a new shopping list service
func NewShoppingListService(
	shoppingListRepo domain.ShoppingListRepository,
	pantryRepo domain.PantryRepository,
	userRepo domain.UserRepository,
	authService domain.PantryAuthorizationService,
) domain.ShoppingListService {
	return &ShoppingListService{
		shoppingListRepo: shoppingListRepo,
		pantryRepo:       pantryRepo,
		userRepo:         userRepo,
		authService:      authService,
	}
}

// CreateShoppingList creates a new shopping list
func (s *ShoppingListService) CreateShoppingList(ctx context.Context, req domain.CreateShoppingListRequest) (*domain.ShoppingList, error) {
	// Validate user exists
	_, err := s.userRepo.GetByID(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	// Validate pantry exists and user has access
	_, err = s.pantryRepo.GetByID(req.PantryID)
	if err != nil {
		return nil, fmt.Errorf("pantry not found: %w", err)
	}

	// Check if user has access to the pantry
	hasAccess, err := s.authService.IsMember(req.UserID, req.PantryID)
	if err != nil {
		return nil, fmt.Errorf("failed to check pantry access: %w", err)
	}
	if !hasAccess {
		return nil, fmt.Errorf("user does not have access to pantry")
	}

	// Create shopping list
	shoppingList := domain.NewShoppingList(req.PantryID, req.Name, req.Description, req.UserID)

	// Validate shopping list
	if !shoppingList.IsValid() {
		return nil, fmt.Errorf("invalid shopping list data")
	}

	// Save to repository
	if err := s.shoppingListRepo.Create(ctx, shoppingList); err != nil {
		return nil, fmt.Errorf("failed to create shopping list: %w", err)
	}

	return shoppingList, nil
}

// GetShoppingList retrieves a shopping list by ID
func (s *ShoppingListService) GetShoppingList(ctx context.Context, id uuid.UUID, userID uuid.UUID) (*domain.ShoppingList, error) {
	// Get shopping list
	shoppingList, err := s.shoppingListRepo.GetByID(ctx, id)
	if err != nil {
		return nil, errors.New(errors.ErrCodeNotFound, "Shopping list not found")
	}

	// Check if user has access to the pantry
	hasAccess, err := s.authService.IsMember(userID, shoppingList.PantryID)
	if err != nil {
		return nil, fmt.Errorf("failed to check pantry access: %w", err)
	}
	if !hasAccess {
		return nil, fmt.Errorf("user does not have access to this shopping list")
	}

	return shoppingList, nil
}

// GetShoppingListsByPantry retrieves shopping lists for a pantry
func (s *ShoppingListService) GetShoppingListsByPantry(ctx context.Context, pantryID uuid.UUID, userID uuid.UUID, filters domain.ShoppingListFilters) ([]*domain.ShoppingList, error) {
	// Check if user has access to the pantry
	hasAccess, err := s.authService.IsMember(userID, pantryID)
	if err != nil {
		return nil, fmt.Errorf("failed to check pantry access: %w", err)
	}
	if !hasAccess {
		return nil, fmt.Errorf("user does not have access to pantry")
	}

	// Get shopping lists
	shoppingLists, err := s.shoppingListRepo.GetByPantryID(ctx, pantryID, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to get shopping lists: %w", err)
	}

	return shoppingLists, nil
}

// UpdateShoppingList updates a shopping list
func (s *ShoppingListService) UpdateShoppingList(ctx context.Context, req domain.UpdateShoppingListRequest) (*domain.ShoppingList, error) {
	// Get existing shopping list
	shoppingList, err := s.shoppingListRepo.GetByID(ctx, req.ID)
	if err != nil {
		return nil, fmt.Errorf("shopping list not found: %w", err)
	}

	// Check if user has access to the pantry
	hasAccess, err := s.authService.IsMember(req.UserID, shoppingList.PantryID)
	if err != nil {
		return nil, fmt.Errorf("failed to check pantry access: %w", err)
	}
	if !hasAccess {
		return nil, fmt.Errorf("user does not have access to this shopping list")
	}

	// Update shopping list
	shoppingList.Update(req.Name, req.Description)

	// Validate updated shopping list
	if !shoppingList.IsValid() {
		return nil, fmt.Errorf("invalid shopping list data")
	}

	// Save to repository
	if err := s.shoppingListRepo.Update(ctx, shoppingList); err != nil {
		return nil, fmt.Errorf("failed to update shopping list: %w", err)
	}

	return shoppingList, nil
}

// DeleteShoppingList deletes a shopping list
func (s *ShoppingListService) DeleteShoppingList(ctx context.Context, id uuid.UUID, userID uuid.UUID) error {
	// Get shopping list
	shoppingList, err := s.shoppingListRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("shopping list not found: %w", err)
	}

	// Check if user has access to the pantry
	hasAccess, err := s.authService.IsMember(userID, shoppingList.PantryID)
	if err != nil {
		return fmt.Errorf("failed to check pantry access: %w", err)
	}
	if !hasAccess {
		return fmt.Errorf("user does not have access to this shopping list")
	}

	// Delete shopping list
	if err := s.shoppingListRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete shopping list: %w", err)
	}

	return nil
}

// ChangeShoppingListStatus changes the status of a shopping list
func (s *ShoppingListService) ChangeShoppingListStatus(ctx context.Context, req domain.ChangeShoppingListStatusRequest) (*domain.ShoppingList, error) {
	// Get shopping list
	shoppingList, err := s.shoppingListRepo.GetByID(ctx, req.ID)
	if err != nil {
		return nil, fmt.Errorf("shopping list not found: %w", err)
	}

	// Check if user has access to the pantry
	hasAccess, err := s.authService.IsMember(req.UserID, shoppingList.PantryID)
	if err != nil {
		return nil, fmt.Errorf("failed to check pantry access: %w", err)
	}
	if !hasAccess {
		return nil, fmt.Errorf("user does not have access to this shopping list")
	}

	// Change status
	shoppingList.ChangeStatus(req.Status)

	// Save to repository
	if err := s.shoppingListRepo.Update(ctx, shoppingList); err != nil {
		return nil, fmt.Errorf("failed to update shopping list status: %w", err)
	}

	return shoppingList, nil
}

// AddItemToShoppingList adds an item to a shopping list
func (s *ShoppingListService) AddItemToShoppingList(ctx context.Context, req domain.AddShoppingListItemRequest) (*domain.ShoppingListItemEntity, error) {
	// Validate request
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("invalid request: %w", err)
	}

	// Get shopping list
	shoppingList, err := s.shoppingListRepo.GetByID(ctx, req.ShoppingListID)
	if err != nil {
		return nil, fmt.Errorf("shopping list not found: %w", err)
	}

	// Check if user has access to the pantry
	hasAccess, err := s.authService.IsMember(req.UserID, shoppingList.PantryID)
	if err != nil {
		return nil, fmt.Errorf("failed to check pantry access: %w", err)
	}
	if !hasAccess {
		return nil, fmt.Errorf("user does not have access to this shopping list")
	}

	// Create shopping list item
	item := domain.NewShoppingListItem(
		req.ShoppingListID,
		req.ProductVariantID,
		req.FreeTextName,
		req.QuantityDesired,
		req.UnitOfMeasureID,
		req.Notes,
	)

	// Validate item
	if !item.IsValid() {
		return nil, fmt.Errorf("invalid shopping list item data")
	}

	// Save to repository
	if err := s.shoppingListRepo.AddItem(ctx, item); err != nil {
		return nil, fmt.Errorf("failed to add item to shopping list: %w", err)
	}

	return item, nil
}

// UpdateShoppingListItem updates a shopping list item
func (s *ShoppingListService) UpdateShoppingListItem(ctx context.Context, req domain.UpdateShoppingListItemRequest) (*domain.ShoppingListItemEntity, error) {
	// Validate request
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("invalid request: %w", err)
	}

	// Get shopping list item
	item, err := s.shoppingListRepo.GetItemByID(ctx, req.ID)
	if err != nil {
		return nil, fmt.Errorf("shopping list item not found: %w", err)
	}

	// Get shopping list to check access
	shoppingList, err := s.shoppingListRepo.GetByID(ctx, item.ShoppingListID)
	if err != nil {
		return nil, fmt.Errorf("shopping list not found: %w", err)
	}

	// Check if user has access to the pantry
	hasAccess, err := s.authService.IsMember(req.UserID, shoppingList.PantryID)
	if err != nil {
		return nil, fmt.Errorf("failed to check pantry access: %w", err)
	}
	if !hasAccess {
		return nil, fmt.Errorf("user does not have access to this shopping list")
	}

	// Update item
	item.UpdateItem(
		req.ProductVariantID,
		req.FreeTextName,
		req.QuantityDesired,
		req.UnitOfMeasureID,
		req.Notes,
	)

	// Validate updated item
	if !item.IsValid() {
		return nil, fmt.Errorf("invalid shopping list item data")
	}

	// Save to repository
	if err := s.shoppingListRepo.UpdateItem(ctx, item); err != nil {
		return nil, fmt.Errorf("failed to update shopping list item: %w", err)
	}

	return item, nil
}

// RemoveItemFromShoppingList removes an item from a shopping list
func (s *ShoppingListService) RemoveItemFromShoppingList(ctx context.Context, req domain.RemoveShoppingListItemRequest) error {
	// Get shopping list item
	item, err := s.shoppingListRepo.GetItemByID(ctx, req.ID)
	if err != nil {
		return fmt.Errorf("shopping list item not found: %w", err)
	}

	// Get shopping list to check access
	shoppingList, err := s.shoppingListRepo.GetByID(ctx, item.ShoppingListID)
	if err != nil {
		return fmt.Errorf("shopping list not found: %w", err)
	}

	// Check if user has access to the pantry
	hasAccess, err := s.authService.IsMember(req.UserID, shoppingList.PantryID)
	if err != nil {
		return fmt.Errorf("failed to check pantry access: %w", err)
	}
	if !hasAccess {
		return fmt.Errorf("user does not have access to this shopping list")
	}

	// Remove item
	if err := s.shoppingListRepo.RemoveItem(ctx, req.ID); err != nil {
		return fmt.Errorf("failed to remove item from shopping list: %w", err)
	}

	return nil
}

// MarkItemAsPurchased marks an item as purchased
func (s *ShoppingListService) MarkItemAsPurchased(ctx context.Context, req domain.MarkItemPurchasedRequest) (*domain.ShoppingListItemEntity, error) {
	// Get shopping list item
	item, err := s.shoppingListRepo.GetItemByID(ctx, req.ID)
	if err != nil {
		return nil, fmt.Errorf("shopping list item not found: %w", err)
	}

	// Get shopping list to check access
	shoppingList, err := s.shoppingListRepo.GetByID(ctx, item.ShoppingListID)
	if err != nil {
		return nil, fmt.Errorf("shopping list not found: %w", err)
	}

	// Check if user has access to the pantry
	hasAccess, err := s.authService.IsMember(req.UserID, shoppingList.PantryID)
	if err != nil {
		return nil, fmt.Errorf("failed to check pantry access: %w", err)
	}
	if !hasAccess {
		return nil, fmt.Errorf("user does not have access to this shopping list")
	}

	// Mark as purchased with price information if provided
	if req.IsPurchased {
		if req.ActualPrice != nil || req.PricePerUnit != nil {
			item.MarkAsPurchasedWithPrice(req.ActualPrice, req.PricePerUnit)
		} else {
			item.MarkAsPurchased()
		}
	} else {
		item.MarkAsNotPurchased()
	}

	// Save to repository
	if err := s.shoppingListRepo.UpdateItem(ctx, item); err != nil {
		return nil, fmt.Errorf("failed to update item purchase status: %w", err)
	}

	return item, nil
}

// MarkItemAsNotPurchased marks an item as not purchased
func (s *ShoppingListService) MarkItemAsNotPurchased(ctx context.Context, req domain.MarkItemNotPurchasedRequest) (*domain.ShoppingListItemEntity, error) {
	// Get shopping list item
	item, err := s.shoppingListRepo.GetItemByID(ctx, req.ID)
	if err != nil {
		return nil, fmt.Errorf("shopping list item not found: %w", err)
	}

	// Get shopping list to check access
	shoppingList, err := s.shoppingListRepo.GetByID(ctx, item.ShoppingListID)
	if err != nil {
		return nil, fmt.Errorf("shopping list not found: %w", err)
	}

	// Check if user has access to the pantry
	hasAccess, err := s.authService.IsMember(req.UserID, shoppingList.PantryID)
	if err != nil {
		return nil, fmt.Errorf("failed to check pantry access: %w", err)
	}
	if !hasAccess {
		return nil, fmt.Errorf("user does not have access to this shopping list")
	}

	// Mark as not purchased
	item.MarkAsNotPurchased()

	// Save to repository
	if err := s.shoppingListRepo.UpdateItem(ctx, item); err != nil {
		return nil, fmt.Errorf("failed to mark item as not purchased: %w", err)
	}

	return item, nil
}

// MarkMultipleItemsAsPurchased marks multiple items as purchased
func (s *ShoppingListService) MarkMultipleItemsAsPurchased(ctx context.Context, req domain.BulkMarkItemsPurchasedRequest) error {
	// Validate that all items belong to shopping lists the user has access to
	for _, itemID := range req.ItemIDs {
		item, err := s.shoppingListRepo.GetItemByID(ctx, itemID)
		if err != nil {
			return fmt.Errorf("shopping list item %s not found: %w", itemID, err)
		}

		// Get shopping list to check access
		shoppingList, err := s.shoppingListRepo.GetByID(ctx, item.ShoppingListID)
		if err != nil {
			return fmt.Errorf("shopping list not found: %w", err)
		}

		// Check if user has access to the pantry
		hasAccess, err := s.authService.IsMember(req.UserID, shoppingList.PantryID)
		if err != nil {
			return fmt.Errorf("failed to check pantry access: %w", err)
		}
		if !hasAccess {
			return fmt.Errorf("user does not have access to shopping list item %s", itemID)
		}
	}

	// Mark all items as purchased
	if err := s.shoppingListRepo.MarkItemsAsPurchased(ctx, req.ItemIDs); err != nil {
		return fmt.Errorf("failed to mark items as purchased: %w", err)
	}

	return nil
}

// MarkMultipleItemsAsNotPurchased marks multiple items as not purchased
func (s *ShoppingListService) MarkMultipleItemsAsNotPurchased(ctx context.Context, req domain.BulkMarkItemsNotPurchasedRequest) error {
	// Validate that all items belong to shopping lists the user has access to
	for _, itemID := range req.ItemIDs {
		item, err := s.shoppingListRepo.GetItemByID(ctx, itemID)
		if err != nil {
			return fmt.Errorf("shopping list item %s not found: %w", itemID, err)
		}

		// Get shopping list to check access
		shoppingList, err := s.shoppingListRepo.GetByID(ctx, item.ShoppingListID)
		if err != nil {
			return fmt.Errorf("shopping list not found: %w", err)
		}

		// Check if user has access to the pantry
		hasAccess, err := s.authService.IsMember(req.UserID, shoppingList.PantryID)
		if err != nil {
			return fmt.Errorf("failed to check pantry access: %w", err)
		}
		if !hasAccess {
			return fmt.Errorf("user does not have access to shopping list item %s", itemID)
		}
	}

	// Mark all items as not purchased
	if err := s.shoppingListRepo.MarkItemsAsNotPurchased(ctx, req.ItemIDs); err != nil {
		return fmt.Errorf("failed to mark items as not purchased: %w", err)
	}

	return nil
}

// GetShoppingListStatistics retrieves statistics for a shopping list
func (s *ShoppingListService) GetShoppingListStatistics(ctx context.Context, shoppingListID uuid.UUID, userID uuid.UUID) (*domain.ShoppingListStats, error) {
	// Get shopping list to check access
	shoppingList, err := s.shoppingListRepo.GetByID(ctx, shoppingListID)
	if err != nil {
		return nil, fmt.Errorf("shopping list not found: %w", err)
	}

	// Check if user has access to the pantry
	hasAccess, err := s.authService.IsMember(userID, shoppingList.PantryID)
	if err != nil {
		return nil, fmt.Errorf("failed to check pantry access: %w", err)
	}
	if !hasAccess {
		return nil, fmt.Errorf("user does not have access to this shopping list")
	}

	// Get statistics
	stats, err := s.shoppingListRepo.GetShoppingListStats(ctx, shoppingListID)
	if err != nil {
		return nil, fmt.Errorf("failed to get shopping list statistics: %w", err)
	}

	return stats, nil
}

// GetPantryShoppingListStatistics retrieves statistics for all shopping lists in a pantry
func (s *ShoppingListService) GetPantryShoppingListStatistics(ctx context.Context, pantryID uuid.UUID, userID uuid.UUID) (*domain.PantryShoppingListStats, error) {
	// Check if user has access to the pantry
	hasAccess, err := s.authService.IsMember(userID, pantryID)
	if err != nil {
		return nil, fmt.Errorf("failed to check pantry access: %w", err)
	}
	if !hasAccess {
		return nil, fmt.Errorf("user does not have access to pantry")
	}

	// Get statistics
	stats, err := s.shoppingListRepo.GetPantryShoppingListStats(ctx, pantryID)
	if err != nil {
		return nil, fmt.Errorf("failed to get pantry shopping list statistics: %w", err)
	}

	return stats, nil
}

// CompleteShoppingList completes a shopping list with store and receipt information
func (s *ShoppingListService) CompleteShoppingList(ctx context.Context, req domain.CompleteShoppingListRequest) (*domain.ShoppingList, error) {
	// Get shopping list
	shoppingList, err := s.shoppingListRepo.GetByID(ctx, req.ID)
	if err != nil {
		return nil, fmt.Errorf("shopping list not found: %w", err)
	}

	// Check if user has access to the pantry
	hasAccess, err := s.authService.IsMember(req.UserID, shoppingList.PantryID)
	if err != nil {
		return nil, fmt.Errorf("failed to check pantry access: %w", err)
	}
	if !hasAccess {
		return nil, fmt.Errorf("user does not have access to this shopping list")
	}

	// Complete the shopping list
	shoppingList.CompleteShoppingList(
		req.StoreName,
		req.StoreAddress,
		req.StoreContact,
		req.ReceiptImageURL,
		req.TotalAmount,
	)

	// Save to repository
	if err := s.shoppingListRepo.Update(ctx, shoppingList); err != nil {
		return nil, fmt.Errorf("failed to complete shopping list: %w", err)
	}

	return shoppingList, nil
}

// UpdateStoreInformation updates store information for a shopping list
func (s *ShoppingListService) UpdateStoreInformation(ctx context.Context, req domain.UpdateStoreInformationRequest) (*domain.ShoppingList, error) {
	// Get shopping list
	shoppingList, err := s.shoppingListRepo.GetByID(ctx, req.ID)
	if err != nil {
		return nil, fmt.Errorf("shopping list not found: %w", err)
	}

	// Check if user has access to the pantry
	hasAccess, err := s.authService.IsMember(req.UserID, shoppingList.PantryID)
	if err != nil {
		return nil, fmt.Errorf("failed to check pantry access: %w", err)
	}
	if !hasAccess {
		return nil, fmt.Errorf("user does not have access to this shopping list")
	}

	// Update store information
	shoppingList.UpdateStoreInformation(req.StoreName, req.StoreAddress, req.StoreContact)

	// Save to repository
	if err := s.shoppingListRepo.Update(ctx, shoppingList); err != nil {
		return nil, fmt.Errorf("failed to update store information: %w", err)
	}

	return shoppingList, nil
}

// GetPurchaseHistory retrieves completed shopping lists as purchase history
func (s *ShoppingListService) GetPurchaseHistory(ctx context.Context, pantryID uuid.UUID, userID uuid.UUID, filters domain.ShoppingListFilters) ([]*domain.ShoppingList, error) {
	// Check if user has access to the pantry
	hasAccess, err := s.authService.IsMember(userID, pantryID)
	if err != nil {
		return nil, fmt.Errorf("failed to check pantry access: %w", err)
	}
	if !hasAccess {
		return nil, fmt.Errorf("user does not have access to pantry")
	}

	// Set filter to only show completed shopping lists
	completedStatus := domain.ShoppingListStatusCompleted
	filters.Status = &completedStatus

	// Get completed shopping lists
	shoppingLists, err := s.shoppingListRepo.GetByPantryID(ctx, pantryID, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to get purchase history: %w", err)
	}

	return shoppingLists, nil
}

// UpdateShoppingListItemPrice updates price information for a shopping list item
func (s *ShoppingListService) UpdateShoppingListItemPrice(ctx context.Context, req domain.UpdateShoppingListItemPriceRequest) (*domain.ShoppingListItemEntity, error) {
	// Get shopping list item
	item, err := s.shoppingListRepo.GetItemByID(ctx, req.ID)
	if err != nil {
		return nil, fmt.Errorf("shopping list item not found: %w", err)
	}

	// Get shopping list to check access
	shoppingList, err := s.shoppingListRepo.GetByID(ctx, item.ShoppingListID)
	if err != nil {
		return nil, fmt.Errorf("shopping list not found: %w", err)
	}

	// Check if user has access to the pantry
	hasAccess, err := s.authService.IsMember(req.UserID, shoppingList.PantryID)
	if err != nil {
		return nil, fmt.Errorf("failed to check pantry access: %w", err)
	}
	if !hasAccess {
		return nil, fmt.Errorf("user does not have access to this shopping list")
	}

	// Update price information
	item.UpdatePriceInformation(req.EstimatedPrice, req.ActualPrice, req.PricePerUnit)

	// Save to repository
	if err := s.shoppingListRepo.UpdateItem(ctx, item); err != nil {
		return nil, fmt.Errorf("failed to update item price information: %w", err)
	}

	return item, nil
}
