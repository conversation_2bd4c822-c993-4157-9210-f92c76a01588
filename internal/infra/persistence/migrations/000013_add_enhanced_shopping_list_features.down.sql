-- Migration: Rollback Enhanced Shopping List Features
-- Description: Remove receipt image storage, store information tracking, price tracking, and purchase history features
-- Date: 2025-06-12

-- Remove indexes
DROP INDEX IF EXISTS idx_shopping_lists_completed_at;
DROP INDEX IF EXISTS idx_shopping_lists_store_name;
DROP INDEX IF EXISTS idx_shopping_lists_total_amount;
DROP INDEX IF EXISTS idx_shopping_list_items_actual_price;
DROP INDEX IF EXISTS idx_shopping_list_items_estimated_price;

-- Remove enhanced fields from shopping_list_items table
ALTER TABLE shopping_list_items 
DROP COLUMN IF EXISTS estimated_price,
DROP COLUMN IF EXISTS actual_price,
DROP COLUMN IF EXISTS price_per_unit;

-- Remove enhanced fields from shopping_lists table
ALTER TABLE shopping_lists 
DROP COLUMN IF EXISTS receipt_image_url,
DROP COLUMN IF EXISTS store_name,
DROP COLUMN IF EXISTS store_address,
DROP COLUMN IF EXISTS store_contact,
DROP COLUMN IF EXISTS total_amount,
DROP COLUMN IF EXISTS completed_at;
