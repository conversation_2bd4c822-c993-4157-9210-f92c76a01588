-- Migration: Add Enhanced Shopping List Features
-- Description: Add receipt image storage, store information tracking, price tracking, and purchase history features
-- Date: 2025-06-12

-- Add enhanced fields to shopping_lists table
ALTER TABLE shopping_lists 
ADD COLUMN receipt_image_url VARCHAR(500),
ADD COLUMN store_name VARCHAR(255),
ADD COLUMN store_address VARCHAR(500),
ADD COLUMN store_contact VARCHAR(255),
ADD COLUMN total_amount DECIMAL(10,2),
ADD COLUMN completed_at TIMESTAMP WITH TIME ZONE;

-- Add price tracking fields to shopping_list_items table
ALTER TABLE shopping_list_items 
ADD COLUMN estimated_price DECIMAL(10,2),
ADD COLUMN actual_price DECIMAL(10,2),
ADD COLUMN price_per_unit DECIMAL(10,2);

-- Add indexes for better query performance
CREATE INDEX idx_shopping_lists_completed_at ON shopping_lists(completed_at);
CREATE INDEX idx_shopping_lists_store_name ON shopping_lists(store_name);
CREATE INDEX idx_shopping_lists_total_amount ON shopping_lists(total_amount);
CREATE INDEX idx_shopping_list_items_actual_price ON shopping_list_items(actual_price);
CREATE INDEX idx_shopping_list_items_estimated_price ON shopping_list_items(estimated_price);

-- Add comments for documentation
COMMENT ON COLUMN shopping_lists.receipt_image_url IS 'URL to the receipt image stored for completed shopping lists';
COMMENT ON COLUMN shopping_lists.store_name IS 'Name of the store where shopping was completed';
COMMENT ON COLUMN shopping_lists.store_address IS 'Address of the store where shopping was completed';
COMMENT ON COLUMN shopping_lists.store_contact IS 'Contact information of the store (phone, website, etc.)';
COMMENT ON COLUMN shopping_lists.total_amount IS 'Total amount spent on the shopping list';
COMMENT ON COLUMN shopping_lists.completed_at IS 'Timestamp when the shopping list was completed';

COMMENT ON COLUMN shopping_list_items.estimated_price IS 'Estimated price for the item before shopping';
COMMENT ON COLUMN shopping_list_items.actual_price IS 'Actual price paid for the item';
COMMENT ON COLUMN shopping_list_items.price_per_unit IS 'Price per unit of measure for the item';
