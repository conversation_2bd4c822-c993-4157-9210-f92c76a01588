package handler

import (
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// ShoppingListHandler handles HTTP requests for shopping list operations
type ShoppingListHandler struct {
	shoppingListService domain.ShoppingListService
	logger              logger.Logger
	validator           *validator.Validate
}

// NewShoppingListHandler creates a new shopping list handler
func NewShoppingListHandler(shoppingListService domain.ShoppingListService, logger logger.Logger) *ShoppingListHandler {
	return &ShoppingListHandler{
		shoppingListService: shoppingListService,
		logger:              logger,
		validator:           validator.New(),
	}
}

// CreateShoppingList creates a new shopping list
//
//	@Summary		Create shopping list
//	@Description	Create a new shopping list in a pantry
//	@Tags			Shopping Lists
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string							true	"Pantry ID"
//	@Param			request		body		domain.CreateShoppingListRequest	true	"Shopping list data"
//	@Success		201			{object}	APIResponse{data=domain.ShoppingList}	"Shopping list created successfully"
//	@Failure		400			{object}	APIResponse							"Invalid request"
//	@Failure		401			{object}	APIResponse							"Unauthorized"
//	@Failure		403			{object}	APIResponse							"Forbidden"
//	@Failure		404			{object}	APIResponse							"Pantry not found"
//	@Failure		500			{object}	APIResponse							"Internal server error"
//	@Router			/pantries/{pantryId}/shopping-lists [post]
func (h *ShoppingListHandler) CreateShoppingList(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	var req domain.CreateShoppingListRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	// Set user ID from context
	req.UserID = userID
	req.PantryID = pantryID

	shoppingList, err := h.shoppingListService.CreateShoppingList(c.Context(), req)
	if err != nil {
		h.logger.LogError(err, "Failed to create shopping list", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
			"name":      req.Name,
		})
		return ErrorResponse(c, err)
	}

	h.logger.LogBusinessEvent("shopping_list.created", shoppingList.ID.String(), map[string]interface{}{
		"user_id":   userIDStr,
		"pantry_id": pantryIDStr,
		"name":      shoppingList.Name,
	})

	return CreatedResponse(c, shoppingList, "Shopping list created successfully")
}

// GetShoppingLists retrieves shopping lists for a pantry
//
//	@Summary		Get shopping lists
//	@Description	Get all shopping lists for a pantry with optional filtering
//	@Tags			Shopping Lists
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string	true	"Pantry ID"
//	@Param			status		query		string	false	"Filter by status (active, completed, archived)"
//	@Param			created_by	query		string	false	"Filter by creator user ID"
//	@Param			page		query		int		false	"Page number (default: 1)"
//	@Param			limit		query		int		false	"Items per page (default: 20, max: 100)"
//	@Param			sort_by		query		string	false	"Sort field (created_at, updated_at, name)"
//	@Param			sort_order	query		string	false	"Sort direction (asc, desc)"
//	@Success		200			{object}	APIResponse{data=[]domain.ShoppingList}	"Shopping lists retrieved successfully"
//	@Failure		400			{object}	APIResponse								"Invalid request"
//	@Failure		401			{object}	APIResponse								"Unauthorized"
//	@Failure		403			{object}	APIResponse								"Forbidden"
//	@Failure		404			{object}	APIResponse								"Pantry not found"
//	@Failure		500			{object}	APIResponse								"Internal server error"
//	@Router			/pantries/{pantryId}/shopping-lists [get]
func (h *ShoppingListHandler) GetShoppingLists(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	// Parse filters
	filters := domain.ShoppingListFilters{}

	if status := c.Query("status"); status != "" {
		statusEnum := domain.ShoppingListStatus(status)
		filters.Status = &statusEnum
	}

	if createdBy := c.Query("created_by"); createdBy != "" {
		createdByID, err := uuid.Parse(createdBy)
		if err != nil {
			return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid created_by ID"))
		}
		filters.CreatedBy = &createdByID
	}

	// Parse pagination
	page, limit := ParsePaginationParams(c)
	filters.Offset = &[]int{(page - 1) * limit}[0]
	filters.Limit = &limit

	// Parse sorting
	if sortBy := c.Query("sort_by"); sortBy != "" {
		filters.SortBy = &sortBy
	}
	if sortOrder := c.Query("sort_order"); sortOrder != "" {
		filters.SortOrder = &sortOrder
	}

	shoppingLists, err := h.shoppingListService.GetShoppingListsByPantry(c.Context(), pantryID, userID, filters)
	if err != nil {
		h.logger.LogError(err, "Failed to get shopping lists", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Calculate pagination
	total := len(shoppingLists)
	pagination := CalculatePagination(page, limit, int64(total))

	return PaginatedSuccessResponse(c, shoppingLists, pagination, "Shopping lists retrieved successfully")
}

// GetShoppingList retrieves a specific shopping list
//
//	@Summary		Get shopping list
//	@Description	Get a specific shopping list by ID
//	@Tags			Shopping Lists
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string	true	"Pantry ID"
//	@Param			listId		path		string	true	"Shopping list ID"
//	@Success		200			{object}	APIResponse{data=domain.ShoppingList}	"Shopping list retrieved successfully"
//	@Failure		400			{object}	APIResponse							"Invalid request"
//	@Failure		401			{object}	APIResponse							"Unauthorized"
//	@Failure		403			{object}	APIResponse							"Forbidden"
//	@Failure		404			{object}	APIResponse							"Shopping list not found"
//	@Failure		500			{object}	APIResponse							"Internal server error"
//	@Router			/pantries/{pantryId}/shopping-lists/{listId} [get]
func (h *ShoppingListHandler) GetShoppingList(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	listIDStr := c.Params("listId")
	listID, err := uuid.Parse(listIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid shopping list ID"))
	}

	shoppingList, err := h.shoppingListService.GetShoppingList(c.Context(), listID, userID)
	if err != nil {
		h.logger.LogError(err, "Failed to get shopping list", map[string]interface{}{
			"user_id": userIDStr,
			"list_id": listIDStr,
		})
		return ErrorResponse(c, err)
	}

	return SuccessResponse(c, shoppingList, "Shopping list retrieved successfully")
}

// UpdateShoppingList updates a shopping list
//
//	@Summary		Update shopping list
//	@Description	Update shopping list details
//	@Tags			Shopping Lists
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string							true	"Pantry ID"
//	@Param			listId		path		string							true	"Shopping list ID"
//	@Param			request		body		domain.UpdateShoppingListRequest	true	"Updated shopping list data"
//	@Success		200			{object}	APIResponse{data=domain.ShoppingList}	"Shopping list updated successfully"
//	@Failure		400			{object}	APIResponse							"Invalid request"
//	@Failure		401			{object}	APIResponse							"Unauthorized"
//	@Failure		403			{object}	APIResponse							"Forbidden"
//	@Failure		404			{object}	APIResponse							"Shopping list not found"
//	@Failure		500			{object}	APIResponse							"Internal server error"
//	@Router			/pantries/{pantryId}/shopping-lists/{listId} [put]
func (h *ShoppingListHandler) UpdateShoppingList(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	listIDStr := c.Params("listId")
	listID, err := uuid.Parse(listIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid shopping list ID"))
	}

	var req domain.UpdateShoppingListRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	// Set IDs from context and params
	req.ID = listID
	req.UserID = userID

	shoppingList, err := h.shoppingListService.UpdateShoppingList(c.Context(), req)
	if err != nil {
		h.logger.LogError(err, "Failed to update shopping list", map[string]interface{}{
			"user_id": userIDStr,
			"list_id": listIDStr,
		})
		return ErrorResponse(c, err)
	}

	h.logger.LogBusinessEvent("shopping_list.updated", shoppingList.ID.String(), map[string]interface{}{
		"user_id": userIDStr,
		"list_id": listIDStr,
		"name":    shoppingList.Name,
	})

	return SuccessResponse(c, shoppingList, "Shopping list updated successfully")
}

// DeleteShoppingList deletes a shopping list
//
//	@Summary		Delete shopping list
//	@Description	Delete a shopping list and all its items
//	@Tags			Shopping Lists
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string	true	"Pantry ID"
//	@Param			listId		path		string	true	"Shopping list ID"
//	@Success		204			{object}	APIResponse	"Shopping list deleted successfully"
//	@Failure		400			{object}	APIResponse	"Invalid request"
//	@Failure		401			{object}	APIResponse	"Unauthorized"
//	@Failure		403			{object}	APIResponse	"Forbidden"
//	@Failure		404			{object}	APIResponse	"Shopping list not found"
//	@Failure		500			{object}	APIResponse	"Internal server error"
//	@Router			/pantries/{pantryId}/shopping-lists/{listId} [delete]
func (h *ShoppingListHandler) DeleteShoppingList(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	listIDStr := c.Params("listId")
	listID, err := uuid.Parse(listIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid shopping list ID"))
	}

	err = h.shoppingListService.DeleteShoppingList(c.Context(), listID, userID)
	if err != nil {
		h.logger.LogError(err, "Failed to delete shopping list", map[string]interface{}{
			"user_id": userIDStr,
			"list_id": listIDStr,
		})
		return ErrorResponse(c, err)
	}

	h.logger.LogBusinessEvent("shopping_list.deleted", listIDStr, map[string]interface{}{
		"user_id": userIDStr,
		"list_id": listIDStr,
	})

	return NoContentResponse(c)
}

// ChangeShoppingListStatus changes the status of a shopping list
//
//	@Summary		Change shopping list status
//	@Description	Change the status of a shopping list (active, completed, archived)
//	@Tags			Shopping Lists
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string								true	"Pantry ID"
//	@Param			listId		path		string								true	"Shopping list ID"
//	@Param			request		body		domain.ChangeShoppingListStatusRequest	true	"Status change data"
//	@Success		200			{object}	APIResponse{data=domain.ShoppingList}		"Shopping list status changed successfully"
//	@Failure		400			{object}	APIResponse								"Invalid request"
//	@Failure		401			{object}	APIResponse								"Unauthorized"
//	@Failure		403			{object}	APIResponse								"Forbidden"
//	@Failure		404			{object}	APIResponse								"Shopping list not found"
//	@Failure		500			{object}	APIResponse								"Internal server error"
//	@Router			/pantries/{pantryId}/shopping-lists/{listId}/status [patch]
func (h *ShoppingListHandler) ChangeShoppingListStatus(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	listIDStr := c.Params("listId")
	listID, err := uuid.Parse(listIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid shopping list ID"))
	}

	var req domain.ChangeShoppingListStatusRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	// Set IDs from context and params
	req.ID = listID
	req.UserID = userID

	shoppingList, err := h.shoppingListService.ChangeShoppingListStatus(c.Context(), req)
	if err != nil {
		h.logger.LogError(err, "Failed to change shopping list status", map[string]interface{}{
			"user_id": userIDStr,
			"list_id": listIDStr,
			"status":  req.Status,
		})
		return ErrorResponse(c, err)
	}

	h.logger.LogBusinessEvent("shopping_list.status_changed", shoppingList.ID.String(), map[string]interface{}{
		"user_id":    userIDStr,
		"list_id":    listIDStr,
		"new_status": shoppingList.Status,
	})

	return SuccessResponse(c, shoppingList, "Shopping list status changed successfully")
}

// AddItemToShoppingList adds an item to a shopping list
//
//	@Summary		Add item to shopping list
//	@Description	Add an item to a shopping list
//	@Tags			Shopping List Items
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string							true	"Pantry ID"
//	@Param			listId		path		string							true	"Shopping list ID"
//	@Param			request		body		domain.AddShoppingListItemRequest	true	"Item data"
//	@Success		201			{object}	APIResponse{data=domain.ShoppingListItemEntity}	"Item added successfully"
//	@Failure		400			{object}	APIResponse									"Invalid request"
//	@Failure		401			{object}	APIResponse									"Unauthorized"
//	@Failure		403			{object}	APIResponse									"Forbidden"
//	@Failure		404			{object}	APIResponse									"Shopping list not found"
//	@Failure		500			{object}	APIResponse									"Internal server error"
//	@Router			/pantries/{pantryId}/shopping-lists/{listId}/items [post]
func (h *ShoppingListHandler) AddItemToShoppingList(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	listIDStr := c.Params("listId")
	listID, err := uuid.Parse(listIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid shopping list ID"))
	}

	var req domain.AddShoppingListItemRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	// Set IDs from context and params
	req.ShoppingListID = listID
	req.UserID = userID

	item, err := h.shoppingListService.AddItemToShoppingList(c.Context(), req)
	if err != nil {
		h.logger.LogError(err, "Failed to add item to shopping list", map[string]interface{}{
			"user_id": userIDStr,
			"list_id": listIDStr,
		})
		return ErrorResponse(c, err)
	}

	h.logger.LogBusinessEvent("shopping_list.item_added", item.ID.String(), map[string]interface{}{
		"user_id":            userIDStr,
		"list_id":            listIDStr,
		"item_id":            item.ID.String(),
		"product_variant_id": item.ProductVariantID,
		"free_text_name":     item.FreeTextName,
	})

	return CreatedResponse(c, item, "Item added to shopping list successfully")
}

// UpdateShoppingListItem updates a shopping list item
//
//	@Summary		Update shopping list item
//	@Description	Update a shopping list item
//	@Tags			Shopping List Items
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string								true	"Pantry ID"
//	@Param			listId		path		string								true	"Shopping list ID"
//	@Param			itemId		path		string								true	"Item ID"
//	@Param			request		body		domain.UpdateShoppingListItemRequest	true	"Updated item data"
//	@Success		200			{object}	APIResponse{data=domain.ShoppingListItemEntity}	"Item updated successfully"
//	@Failure		400			{object}	APIResponse									"Invalid request"
//	@Failure		401			{object}	APIResponse									"Unauthorized"
//	@Failure		403			{object}	APIResponse									"Forbidden"
//	@Failure		404			{object}	APIResponse									"Item not found"
//	@Failure		500			{object}	APIResponse									"Internal server error"
//	@Router			/pantries/{pantryId}/shopping-lists/{listId}/items/{itemId} [put]
func (h *ShoppingListHandler) UpdateShoppingListItem(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	itemIDStr := c.Params("itemId")
	itemID, err := uuid.Parse(itemIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid item ID"))
	}

	var req domain.UpdateShoppingListItemRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	// Set IDs from context and params
	req.ID = itemID
	req.UserID = userID

	item, err := h.shoppingListService.UpdateShoppingListItem(c.Context(), req)
	if err != nil {
		h.logger.LogError(err, "Failed to update shopping list item", map[string]interface{}{
			"user_id": userIDStr,
			"item_id": itemIDStr,
		})
		return ErrorResponse(c, err)
	}

	h.logger.LogBusinessEvent("shopping_list.item_updated", item.ID.String(), map[string]interface{}{
		"user_id": userIDStr,
		"item_id": itemIDStr,
	})

	return SuccessResponse(c, item, "Shopping list item updated successfully")
}

// RemoveItemFromShoppingList removes an item from a shopping list
//
//	@Summary		Remove item from shopping list
//	@Description	Remove an item from a shopping list
//	@Tags			Shopping List Items
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string	true	"Pantry ID"
//	@Param			listId		path		string	true	"Shopping list ID"
//	@Param			itemId		path		string	true	"Item ID"
//	@Success		204			{object}	APIResponse	"Item removed successfully"
//	@Failure		400			{object}	APIResponse	"Invalid request"
//	@Failure		401			{object}	APIResponse	"Unauthorized"
//	@Failure		403			{object}	APIResponse	"Forbidden"
//	@Failure		404			{object}	APIResponse	"Item not found"
//	@Failure		500			{object}	APIResponse	"Internal server error"
//	@Router			/pantries/{pantryId}/shopping-lists/{listId}/items/{itemId} [delete]
func (h *ShoppingListHandler) RemoveItemFromShoppingList(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	itemIDStr := c.Params("itemId")
	itemID, err := uuid.Parse(itemIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid item ID"))
	}

	req := domain.RemoveShoppingListItemRequest{
		ID:     itemID,
		UserID: userID,
	}

	err = h.shoppingListService.RemoveItemFromShoppingList(c.Context(), req)
	if err != nil {
		h.logger.LogError(err, "Failed to remove item from shopping list", map[string]interface{}{
			"user_id": userIDStr,
			"item_id": itemIDStr,
		})
		return ErrorResponse(c, err)
	}

	h.logger.LogBusinessEvent("shopping_list.item_removed", itemIDStr, map[string]interface{}{
		"user_id": userIDStr,
		"item_id": itemIDStr,
	})

	return NoContentResponse(c)
}

// MarkItemAsPurchased marks an item as purchased
//
//	@Summary		Mark item as purchased
//	@Description	Mark a shopping list item as purchased
//	@Tags			Shopping List Items
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string	true	"Pantry ID"
//	@Param			listId		path		string	true	"Shopping list ID"
//	@Param			itemId		path		string	true	"Item ID"
//	@Success		200			{object}	APIResponse{data=domain.ShoppingListItemEntity}	"Item marked as purchased"
//	@Failure		400			{object}	APIResponse									"Invalid request"
//	@Failure		401			{object}	APIResponse									"Unauthorized"
//	@Failure		403			{object}	APIResponse									"Forbidden"
//	@Failure		404			{object}	APIResponse									"Item not found"
//	@Failure		500			{object}	APIResponse									"Internal server error"
//	@Router			/pantries/{pantryId}/shopping-lists/{listId}/items/{itemId}/purchased [patch]
func (h *ShoppingListHandler) MarkItemAsPurchased(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	itemIDStr := c.Params("itemId")
	itemID, err := uuid.Parse(itemIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid item ID"))
	}

	req := domain.MarkItemPurchasedRequest{
		ID:     itemID,
		UserID: userID,
	}

	item, err := h.shoppingListService.MarkItemAsPurchased(c.Context(), req)
	if err != nil {
		h.logger.LogError(err, "Failed to mark item as purchased", map[string]interface{}{
			"user_id": userIDStr,
			"item_id": itemIDStr,
		})
		return ErrorResponse(c, err)
	}

	h.logger.LogBusinessEvent("shopping_list.item_purchased", item.ID.String(), map[string]interface{}{
		"user_id": userIDStr,
		"item_id": itemIDStr,
	})

	return SuccessResponse(c, item, "Item marked as purchased successfully")
}

// MarkItemAsNotPurchased marks an item as not purchased
//
//	@Summary		Mark item as not purchased
//	@Description	Mark a shopping list item as not purchased
//	@Tags			Shopping List Items
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string	true	"Pantry ID"
//	@Param			listId		path		string	true	"Shopping list ID"
//	@Param			itemId		path		string	true	"Item ID"
//	@Success		200			{object}	APIResponse{data=domain.ShoppingListItemEntity}	"Item marked as not purchased"
//	@Failure		400			{object}	APIResponse									"Invalid request"
//	@Failure		401			{object}	APIResponse									"Unauthorized"
//	@Failure		403			{object}	APIResponse									"Forbidden"
//	@Failure		404			{object}	APIResponse									"Item not found"
//	@Failure		500			{object}	APIResponse									"Internal server error"
//	@Router			/pantries/{pantryId}/shopping-lists/{listId}/items/{itemId}/purchased [delete]
func (h *ShoppingListHandler) MarkItemAsNotPurchased(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	itemIDStr := c.Params("itemId")
	itemID, err := uuid.Parse(itemIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid item ID"))
	}

	req := domain.MarkItemNotPurchasedRequest{
		ID:     itemID,
		UserID: userID,
	}

	item, err := h.shoppingListService.MarkItemAsNotPurchased(c.Context(), req)
	if err != nil {
		h.logger.LogError(err, "Failed to mark item as not purchased", map[string]interface{}{
			"user_id": userIDStr,
			"item_id": itemIDStr,
		})
		return ErrorResponse(c, err)
	}

	h.logger.LogBusinessEvent("shopping_list.item_not_purchased", item.ID.String(), map[string]interface{}{
		"user_id": userIDStr,
		"item_id": itemIDStr,
	})

	return SuccessResponse(c, item, "Item marked as not purchased successfully")
}

// BulkMarkItemsAsPurchased marks multiple items as purchased
//
//	@Summary		Bulk mark items as purchased
//	@Description	Mark multiple shopping list items as purchased
//	@Tags			Shopping List Items
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string								true	"Pantry ID"
//	@Param			listId		path		string								true	"Shopping list ID"
//	@Param			request		body		domain.BulkMarkItemsPurchasedRequest	true	"Item IDs to mark as purchased"
//	@Success		200			{object}	APIResponse							"Items marked as purchased successfully"
//	@Failure		400			{object}	APIResponse							"Invalid request"
//	@Failure		401			{object}	APIResponse							"Unauthorized"
//	@Failure		403			{object}	APIResponse							"Forbidden"
//	@Failure		404			{object}	APIResponse							"Items not found"
//	@Failure		500			{object}	APIResponse							"Internal server error"
//	@Router			/pantries/{pantryId}/shopping-lists/{listId}/items/bulk-purchased [patch]
func (h *ShoppingListHandler) BulkMarkItemsAsPurchased(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	var req domain.BulkMarkItemsPurchasedRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	// Set user ID from context
	req.UserID = userID

	err = h.shoppingListService.MarkMultipleItemsAsPurchased(c.Context(), req)
	if err != nil {
		h.logger.LogError(err, "Failed to bulk mark items as purchased", map[string]interface{}{
			"user_id":    userIDStr,
			"item_count": len(req.ItemIDs),
		})
		return ErrorResponse(c, err)
	}

	h.logger.LogBusinessEvent("shopping_list.bulk_items_purchased", "", map[string]interface{}{
		"user_id":    userIDStr,
		"item_count": len(req.ItemIDs),
		"item_ids":   req.ItemIDs,
	})

	return SuccessResponse(c, map[string]interface{}{
		"items_updated": len(req.ItemIDs),
	}, "Items marked as purchased successfully")
}

// BulkMarkItemsAsNotPurchased marks multiple items as not purchased
//
//	@Summary		Bulk mark items as not purchased
//	@Description	Mark multiple shopping list items as not purchased
//	@Tags			Shopping List Items
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string									true	"Pantry ID"
//	@Param			listId		path		string									true	"Shopping list ID"
//	@Param			request		body		domain.BulkMarkItemsNotPurchasedRequest	true	"Item IDs to mark as not purchased"
//	@Success		200			{object}	APIResponse								"Items marked as not purchased successfully"
//	@Failure		400			{object}	APIResponse								"Invalid request"
//	@Failure		401			{object}	APIResponse								"Unauthorized"
//	@Failure		403			{object}	APIResponse								"Forbidden"
//	@Failure		404			{object}	APIResponse								"Items not found"
//	@Failure		500			{object}	APIResponse								"Internal server error"
//	@Router			/pantries/{pantryId}/shopping-lists/{listId}/items/bulk-purchased [delete]
func (h *ShoppingListHandler) BulkMarkItemsAsNotPurchased(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	var req domain.BulkMarkItemsNotPurchasedRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	// Set user ID from context
	req.UserID = userID

	err = h.shoppingListService.MarkMultipleItemsAsNotPurchased(c.Context(), req)
	if err != nil {
		h.logger.LogError(err, "Failed to bulk mark items as not purchased", map[string]interface{}{
			"user_id":    userIDStr,
			"item_count": len(req.ItemIDs),
		})
		return ErrorResponse(c, err)
	}

	h.logger.LogBusinessEvent("shopping_list.bulk_items_not_purchased", "", map[string]interface{}{
		"user_id":    userIDStr,
		"item_count": len(req.ItemIDs),
		"item_ids":   req.ItemIDs,
	})

	return SuccessResponse(c, map[string]interface{}{
		"items_updated": len(req.ItemIDs),
	}, "Items marked as not purchased successfully")
}

// GetShoppingListStatistics retrieves statistics for a shopping list
//
//	@Summary		Get shopping list statistics
//	@Description	Get completion statistics for a specific shopping list
//	@Tags			Shopping List Statistics
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string	true	"Pantry ID"
//	@Param			listId		path		string	true	"Shopping list ID"
//	@Success		200			{object}	APIResponse{data=domain.ShoppingListStats}	"Statistics retrieved successfully"
//	@Failure		400			{object}	APIResponse								"Invalid request"
//	@Failure		401			{object}	APIResponse								"Unauthorized"
//	@Failure		403			{object}	APIResponse								"Forbidden"
//	@Failure		404			{object}	APIResponse								"Shopping list not found"
//	@Failure		500			{object}	APIResponse								"Internal server error"
//	@Router			/pantries/{pantryId}/shopping-lists/{listId}/stats [get]
func (h *ShoppingListHandler) GetShoppingListStatistics(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	listIDStr := c.Params("listId")
	listID, err := uuid.Parse(listIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid shopping list ID"))
	}

	stats, err := h.shoppingListService.GetShoppingListStatistics(c.Context(), listID, userID)
	if err != nil {
		h.logger.LogError(err, "Failed to get shopping list statistics", map[string]interface{}{
			"user_id": userIDStr,
			"list_id": listIDStr,
		})
		return ErrorResponse(c, err)
	}

	return SuccessResponse(c, stats, "Shopping list statistics retrieved successfully")
}

// GetPantryShoppingListStatistics retrieves statistics for all shopping lists in a pantry
//
//	@Summary		Get pantry shopping list statistics
//	@Description	Get aggregated statistics for all shopping lists in a pantry
//	@Tags			Shopping List Statistics
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string	true	"Pantry ID"
//	@Success		200			{object}	APIResponse{data=domain.PantryShoppingListStats}	"Statistics retrieved successfully"
//	@Failure		400			{object}	APIResponse									"Invalid request"
//	@Failure		401			{object}	APIResponse									"Unauthorized"
//	@Failure		403			{object}	APIResponse									"Forbidden"
//	@Failure		404			{object}	APIResponse									"Pantry not found"
//	@Failure		500			{object}	APIResponse									"Internal server error"
//	@Router			/pantries/{pantryId}/shopping-lists/stats [get]
func (h *ShoppingListHandler) GetPantryShoppingListStatistics(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	stats, err := h.shoppingListService.GetPantryShoppingListStatistics(c.Context(), pantryID, userID)
	if err != nil {
		h.logger.LogError(err, "Failed to get pantry shopping list statistics", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, err)
	}

	return SuccessResponse(c, stats, "Pantry shopping list statistics retrieved successfully")
}

// CompleteShoppingList completes a shopping list with store and receipt information
//
//	@Summary		Complete shopping list
//	@Description	Complete a shopping list with store information and receipt details
//	@Tags			Shopping Lists
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string								true	"Pantry ID"
//	@Param			listId		path		string								true	"Shopping list ID"
//	@Param			request		body		domain.CompleteShoppingListRequest	true	"Completion data"
//	@Success		200			{object}	APIResponse{data=domain.ShoppingList}	"Shopping list completed successfully"
//	@Failure		400			{object}	APIResponse								"Invalid request"
//	@Failure		401			{object}	APIResponse								"Unauthorized"
//	@Failure		403			{object}	APIResponse								"Forbidden"
//	@Failure		404			{object}	APIResponse								"Shopping list not found"
//	@Failure		500			{object}	APIResponse								"Internal server error"
//	@Router			/pantries/{pantryId}/shopping-lists/{listId}/complete [post]
func (h *ShoppingListHandler) CompleteShoppingList(c *fiber.Ctx) error {
	userIDStr := c.Locals("userID").(string)
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	_, err = uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	listIDStr := c.Params("listId")
	listID, err := uuid.Parse(listIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid shopping list ID"))
	}

	var req domain.CompleteShoppingListRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	// Set IDs from context and params
	req.ID = listID
	req.UserID = userID

	// Validate request
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	shoppingList, err := h.shoppingListService.CompleteShoppingList(c.Context(), req)
	if err != nil {
		h.logger.LogError(err, "Failed to complete shopping list", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
			"list_id":   listIDStr,
		})
		return ErrorResponse(c, err)
	}

	h.logger.LogBusinessEvent("shopping_list.completed", shoppingList.ID.String(), map[string]interface{}{
		"user_id":      userIDStr,
		"pantry_id":    pantryIDStr,
		"list_id":      listIDStr,
		"store_name":   req.StoreName,
		"total_amount": req.TotalAmount,
	})

	return SuccessResponse(c, shoppingList, "Shopping list completed successfully")
}

// UpdateStoreInformation updates store information for a shopping list
//
//	@Summary		Update store information
//	@Description	Update store information for a shopping list
//	@Tags			Shopping Lists
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string									true	"Pantry ID"
//	@Param			listId		path		string									true	"Shopping list ID"
//	@Param			request		body		domain.UpdateStoreInformationRequest	true	"Store information"
//	@Success		200			{object}	APIResponse{data=domain.ShoppingList}	"Store information updated successfully"
//	@Failure		400			{object}	APIResponse								"Invalid request"
//	@Failure		401			{object}	APIResponse								"Unauthorized"
//	@Failure		403			{object}	APIResponse								"Forbidden"
//	@Failure		404			{object}	APIResponse								"Shopping list not found"
//	@Failure		500			{object}	APIResponse								"Internal server error"
//	@Router			/pantries/{pantryId}/shopping-lists/{listId}/store [put]
func (h *ShoppingListHandler) UpdateStoreInformation(c *fiber.Ctx) error {
	userIDStr := c.Locals("userID").(string)
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	_, err = uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	listIDStr := c.Params("listId")
	listID, err := uuid.Parse(listIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid shopping list ID"))
	}

	var req domain.UpdateStoreInformationRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	// Set IDs from context and params
	req.ID = listID
	req.UserID = userID

	// Validate request
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	shoppingList, err := h.shoppingListService.UpdateStoreInformation(c.Context(), req)
	if err != nil {
		h.logger.LogError(err, "Failed to update store information", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
			"list_id":   listIDStr,
		})
		return ErrorResponse(c, err)
	}

	h.logger.LogBusinessEvent("shopping_list.store_updated", shoppingList.ID.String(), map[string]interface{}{
		"user_id":       userIDStr,
		"pantry_id":     pantryIDStr,
		"list_id":       listIDStr,
		"store_name":    req.StoreName,
		"store_address": req.StoreAddress,
	})

	return SuccessResponse(c, shoppingList, "Store information updated successfully")
}

// GetPurchaseHistory retrieves completed shopping lists as purchase history
//
//	@Summary		Get purchase history
//	@Description	Get completed shopping lists as purchase history for a pantry
//	@Tags			Shopping Lists
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string	true	"Pantry ID"
//	@Param			page		query		int		false	"Page number (default: 1)"
//	@Param			limit		query		int		false	"Items per page (default: 20, max: 100)"
//	@Param			sort_by		query		string	false	"Sort field (created_at, updated_at, completed_at, total_amount)"
//	@Param			sort_order	query		string	false	"Sort order (asc, desc)"
//	@Success		200			{object}	APIResponse{data=[]domain.ShoppingList}	"Purchase history retrieved successfully"
//	@Failure		400			{object}	APIResponse								"Invalid request"
//	@Failure		401			{object}	APIResponse								"Unauthorized"
//	@Failure		403			{object}	APIResponse								"Forbidden"
//	@Failure		500			{object}	APIResponse								"Internal server error"
//	@Router			/pantries/{pantryId}/purchase-history [get]
func (h *ShoppingListHandler) GetPurchaseHistory(c *fiber.Ctx) error {
	userIDStr := c.Locals("userID").(string)
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	// Parse filters
	var filters domain.ShoppingListFilters

	// Parse pagination
	page, limit := ParsePaginationParams(c)
	filters.Offset = &[]int{(page - 1) * limit}[0]
	filters.Limit = &limit

	// Parse sorting
	if sortBy := c.Query("sort_by"); sortBy != "" {
		filters.SortBy = &sortBy
	}
	if sortOrder := c.Query("sort_order"); sortOrder != "" {
		filters.SortOrder = &sortOrder
	}

	shoppingLists, err := h.shoppingListService.GetPurchaseHistory(c.Context(), pantryID, userID, filters)
	if err != nil {
		h.logger.LogError(err, "Failed to get purchase history", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, err)
	}

	h.logger.LogBusinessEvent("shopping_list.purchase_history_viewed", "", map[string]interface{}{
		"user_id":   userIDStr,
		"pantry_id": pantryIDStr,
		"count":     len(shoppingLists),
	})

	return SuccessResponse(c, shoppingLists, "Purchase history retrieved successfully")
}

// UpdateShoppingListItemPrice updates price information for a shopping list item
//
//	@Summary		Update item price information
//	@Description	Update price information for a shopping list item
//	@Tags			Shopping Lists
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string										true	"Pantry ID"
//	@Param			listId		path		string										true	"Shopping list ID"
//	@Param			itemId		path		string										true	"Shopping list item ID"
//	@Param			request		body		domain.UpdateShoppingListItemPriceRequest	true	"Price information"
//	@Success		200			{object}	APIResponse{data=domain.ShoppingListItemEntity}	"Item price updated successfully"
//	@Failure		400			{object}	APIResponse									"Invalid request"
//	@Failure		401			{object}	APIResponse									"Unauthorized"
//	@Failure		403			{object}	APIResponse									"Forbidden"
//	@Failure		404			{object}	APIResponse									"Shopping list item not found"
//	@Failure		500			{object}	APIResponse									"Internal server error"
//	@Router			/pantries/{pantryId}/shopping-lists/{listId}/items/{itemId}/price [put]
func (h *ShoppingListHandler) UpdateShoppingListItemPrice(c *fiber.Ctx) error {
	userIDStr := c.Locals("userID").(string)
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	_, err = uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	listIDStr := c.Params("listId")
	_, err = uuid.Parse(listIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid shopping list ID"))
	}

	itemIDStr := c.Params("itemId")
	itemID, err := uuid.Parse(itemIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid shopping list item ID"))
	}

	var req domain.UpdateShoppingListItemPriceRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	// Set IDs from context and params
	req.ID = itemID
	req.UserID = userID

	// Validate request
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	item, err := h.shoppingListService.UpdateShoppingListItemPrice(c.Context(), req)
	if err != nil {
		h.logger.LogError(err, "Failed to update item price information", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
			"list_id":   listIDStr,
			"item_id":   itemIDStr,
		})
		return ErrorResponse(c, err)
	}

	h.logger.LogBusinessEvent("shopping_list.item_price_updated", item.ID.String(), map[string]interface{}{
		"user_id":         userIDStr,
		"pantry_id":       pantryIDStr,
		"list_id":         listIDStr,
		"item_id":         itemIDStr,
		"estimated_price": req.EstimatedPrice,
		"actual_price":    req.ActualPrice,
		"price_per_unit":  req.PricePerUnit,
	})

	return SuccessResponse(c, item, "Item price information updated successfully")
}

// parseValidationErrors parses validation errors into a map
func (h *ShoppingListHandler) parseValidationErrors(err error) map[string]string {
	validationErrors := make(map[string]string)

	if validationErr, ok := err.(validator.ValidationErrors); ok {
		for _, fieldErr := range validationErr {
			field := strings.ToLower(fieldErr.Field())
			switch fieldErr.Tag() {
			case "required":
				validationErrors[field] = "This field is required"
			case "min":
				validationErrors[field] = "Value is too short"
			case "max":
				validationErrors[field] = "Value is too long"
			case "url":
				validationErrors[field] = "Must be a valid URL"
			case "gte":
				validationErrors[field] = "Value must be greater than or equal to 0"
			case "oneof":
				validationErrors[field] = "Invalid value"
			default:
				validationErrors[field] = "Invalid value"
			}
		}
	}

	return validationErrors
}
