package web

import (
	"context"
	"fmt"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/recover"
	fiberSwagger "github.com/swaggo/fiber-swagger"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/core/services"
	"github.com/wongpinter/pantry-pal/internal/core/usecases"
	"github.com/wongpinter/pantry-pal/internal/infra/auth"
	"github.com/wongpinter/pantry-pal/internal/infra/config"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
	"github.com/wongpinter/pantry-pal/internal/infra/notification"
	"github.com/wongpinter/pantry-pal/internal/infra/persistence/postgres"
	"github.com/wongpinter/pantry-pal/internal/infra/web/handler"
	"github.com/wongpinter/pantry-pal/internal/infra/web/middleware"
)

// Server represents the HTTP server
type Server struct {
	app    *fiber.App
	config *config.Config
	logger *logger.Logger
	db     *gorm.DB
}

// NewServer creates a new HTTP server
func NewServer(cfg *config.Config, log *logger.Logger) (*Server, error) {
	// Initialize database
	db, err := postgres.NewGormDB(cfg.Database)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize database: %w", err)
	}

	return NewServerWithDB(cfg, log, db)
}

// NewServerWithDB creates a new HTTP server with an existing database connection
func NewServerWithDB(cfg *config.Config, log *logger.Logger, db *gorm.DB) (*Server, error) {
	// Create Fiber app
	app := fiber.New(fiber.Config{
		ReadTimeout:  parseDuration(cfg.Server.ReadTimeout, 10*time.Second),
		WriteTimeout: parseDuration(cfg.Server.WriteTimeout, 10*time.Second),
		IdleTimeout:  parseDuration(cfg.Server.IdleTimeout, 120*time.Second),
		BodyLimit:    parseBodyLimit(cfg.Server.BodyLimit, 4*1024*1024), // 4MB default
		Prefork:      cfg.Server.Prefork,
		// Removed custom ErrorHandler - using Fiber's default error handling
	})

	server := &Server{
		app:    app,
		config: cfg,
		logger: log,
		db:     db,
	}

	// Initialize global error logger
	logger.SetGlobalErrorLogger(log)

	// Setup middleware
	server.setupMiddleware()

	// Setup routes
	server.setupRoutes()

	return server, nil
}

// GetApp returns the Fiber app instance
func (s *Server) GetApp() *fiber.App {
	return s.app
}

// Start starts the HTTP server
func (s *Server) Start() error {
	address := fmt.Sprintf("%s:%s", s.config.Server.Host, s.config.Server.Port)
	s.logger.Info().Str("address", address).Msg("Starting HTTP server")

	return s.app.Listen(address)
}

// Shutdown gracefully shuts down the server
func (s *Server) Shutdown(ctx context.Context) error {
	s.logger.Info().Msg("Shutting down HTTP server")

	// Shutdown Fiber app
	if err := s.app.ShutdownWithContext(ctx); err != nil {
		s.logger.Error().Err(err).Msg("Error shutting down HTTP server")
	}

	// Close database connection
	if err := postgres.CloseDB(s.db); err != nil {
		s.logger.Error().Err(err).Msg("Error closing database connection")
	}

	return nil
}

// setupMiddleware configures middleware
func (s *Server) setupMiddleware() {
	// Recovery middleware
	s.app.Use(recover.New(recover.Config{
		EnableStackTrace: s.config.App.Environment == "development",
	}))

	// Request ID middleware
	s.app.Use(middleware.RequestID())

	// Logger middleware
	s.app.Use(middleware.Logger(middleware.LoggerConfig{
		Logger: s.logger,
		SkipPaths: []string{
			"/health",
			"/metrics",
		},
		SkipSuccessfulRequests: false,
	}))

	// CORS middleware
	s.app.Use(cors.New(cors.Config{
		AllowOrigins:     joinStrings(s.config.Server.CORS.AllowOrigins, ","),
		AllowMethods:     joinStrings(s.config.Server.CORS.AllowMethods, ","),
		AllowHeaders:     joinStrings(s.config.Server.CORS.AllowHeaders, ","),
		AllowCredentials: s.config.Server.CORS.AllowCredentials,
		ExposeHeaders:    joinStrings(s.config.Server.CORS.ExposeHeaders, ","),
		MaxAge:           s.config.Server.CORS.MaxAge,
	}))
}

// setupRoutes configures API routes
func (s *Server) setupRoutes() {
	// Health check endpoint
	s.app.Get("/health", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status":    "ok",
			"timestamp": time.Now(),
			"version":   s.config.App.Version,
		})
	})

	// Swagger documentation
	s.app.Get("/docs/*", fiberSwagger.WrapHandler)

	// API routes
	api := s.app.Group("/api/v1")

	// Initialize repositories
	userRepo := postgres.NewUserRepository(s.db)
	refreshTokenRepo := postgres.NewRefreshTokenRepository(s.db)
	pantryRepo := postgres.NewPantryRepository(s.db)
	membershipRepo := postgres.NewPantryMembershipRepository(s.db)
	locationRepo := postgres.NewPantryLocationRepository(s.db)
	inventoryRepo := postgres.NewInventoryItemRepository(s.db)

	// Product catalog repositories
	categoryRepo := postgres.NewCategoryRepository(s.db)
	unitRepo := postgres.NewUnitOfMeasureRepository(s.db)
	productRepo := postgres.NewProductRepository(s.db)
	variantRepo := postgres.NewProductVariantRepository(s.db)

	// Shopping list repository
	shoppingListRepo := postgres.NewShoppingListRepository(s.db)

	// Recipe repositories
	recipeRepo := postgres.NewRecipeRepository(s.db)
	recipeTagRepo := postgres.NewRecipeTagRepository(s.db)
	recipeReviewRepo := postgres.NewRecipeReviewRepository(s.db)
	recipeCollectionRepo := postgres.NewRecipeCollectionRepository(s.db)

	// Initialize auth service
	authService, err := auth.NewJWTAuthService(s.config.Auth.JWT, userRepo, refreshTokenRepo)
	if err != nil {
		s.logger.Fatal().Err(err).Msg("Failed to initialize auth service")
	}

	// Initialize authorization service
	authzService := auth.NewPantryAuthorizationService(pantryRepo, membershipRepo)

	// Initialize shopping list service
	shoppingListService := usecases.NewShoppingListService(
		shoppingListRepo,
		pantryRepo,
		userRepo,
		authzService,
	)

	// Initialize use cases
	loggerAdapter := &LoggerAdapter{logger: s.logger}
	eventDispatcher := usecases.NewSimpleEventDispatcher(loggerAdapter)
	inventoryUsecase := usecases.NewInventoryUsecase(
		inventoryRepo,
		pantryRepo,
		variantRepo,
		productRepo,
		unitRepo,
		locationRepo,
		authzService,
		eventDispatcher,
		loggerAdapter,
	)

	// Create alert configuration repository
	alertConfigRepo := postgres.NewAlertConfigurationRepository(s.db)

	// Create notification repository and service
	notificationRepo := postgres.NewNotificationRepository(s.db)
	notificationLoggerAdapter := &NotificationLoggerAdapter{logger: s.logger}
	notificationService := services.NewNotificationService(notificationRepo, notificationLoggerAdapter)

	// Register notification providers
	logProvider := notification.NewLogProvider(s.logger, true)
	consoleProvider := notification.NewConsoleProvider(s.config.App.Environment == "development")
	notificationService.RegisterProvider(logProvider)
	notificationService.RegisterProvider(consoleProvider)

	// Create expiration use case with all dependencies
	expirationUsecase := usecases.NewExpirationUsecase(
		inventoryRepo,
		pantryRepo,
		locationRepo,
		variantRepo,
		productRepo,
		unitRepo,
		alertConfigRepo,
		authzService,
		notificationService,
		loggerAdapter,
	)

	// Create recipe use case with all dependencies
	recipeUsecase := usecases.NewRecipeUsecase(
		recipeRepo,
		recipeTagRepo,
		recipeReviewRepo,
		recipeCollectionRepo,
		inventoryRepo,
		variantRepo,
		productRepo,
		unitRepo,
		authzService,
		s.logger,
	)

	// Auth routes (public)
	authGroup := api.Group("/auth")
	s.setupAuthRoutes(authGroup, authService, userRepo)

	// Protected routes
	protected := api.Group("")
	protected.Use(middleware.JWTAuth(middleware.JWTAuthConfig{
		AuthService: authService,
	}))

	// User routes
	userGroup := protected.Group("/users")
	s.setupUserRoutes(userGroup, userRepo, authService)

	// Pantry routes
	pantryGroup := protected.Group("/pantries")
	s.setupPantryRoutes(pantryGroup, pantryRepo, membershipRepo, locationRepo, userRepo, authzService, inventoryUsecase, expirationUsecase, shoppingListService)

	// Product catalog routes
	catalogGroup := protected.Group("/catalog")
	s.setupProductCatalogRoutes(catalogGroup, categoryRepo, unitRepo, productRepo, variantRepo)

	// Recipe routes
	recipeGroup := protected.Group("/recipes")
	s.setupRecipeRoutes(recipeGroup, recipeUsecase)

	// Global expiration routes (not pantry-specific)
	if expirationUsecase != nil {
		expirationHandler := handler.NewExpirationHandler(expirationUsecase, s.logger)
		protected.Post("/expiration/alerts/global", expirationHandler.ConfigureAlerts)
		protected.Get("/expiration/alerts/global", expirationHandler.GetAlertConfiguration)
	}
}

// setupAuthRoutes configures authentication routes
func (s *Server) setupAuthRoutes(group fiber.Router, authService domain.AuthService, userRepo domain.UserRepository) {
	authHandler := handler.NewAuthHandler(authService, userRepo, s.logger)

	group.Post("/register", authHandler.Register)
	group.Post("/login", authHandler.Login)
	group.Post("/refresh", authHandler.RefreshToken)
	group.Post("/logout", authHandler.Logout)
}

// setupUserRoutes configures user routes
func (s *Server) setupUserRoutes(group fiber.Router, userRepo domain.UserRepository, authService domain.AuthService) {
	userHandler := handler.NewUserHandler(userRepo, authService, s.logger)

	group.Get("/profile", userHandler.GetProfile)
	group.Put("/profile", userHandler.UpdateProfile)
	group.Post("/change-password", userHandler.ChangePassword)
}

// setupPantryRoutes configures pantry routes
func (s *Server) setupPantryRoutes(
	group fiber.Router,
	pantryRepo domain.PantryRepository,
	membershipRepo domain.PantryMembershipRepository,
	locationRepo domain.PantryLocationRepository,
	userRepo domain.UserRepository,
	authzService domain.PantryAuthorizationService,
	inventoryUsecase *usecases.InventoryUsecase,
	expirationUsecase *usecases.ExpirationUsecase,
	shoppingListService domain.ShoppingListService,
) {
	pantryHandler := handler.NewPantryHandler(pantryRepo, membershipRepo, locationRepo, userRepo, authzService, s.logger)
	membershipHandler := handler.NewPantryMembershipHandler(pantryRepo, membershipRepo, userRepo, authzService, s.logger)
	locationHandler := handler.NewPantryLocationHandler(pantryRepo, locationRepo, authzService, s.logger)
	inventoryHandler := handler.NewInventoryHandler(inventoryUsecase, s.logger)
	shoppingListHandler := handler.NewShoppingListHandler(shoppingListService, *s.logger)

	// Only create expiration handler if use case is available
	var expirationHandler *handler.ExpirationHandler
	if expirationUsecase != nil {
		expirationHandler = handler.NewExpirationHandler(expirationUsecase, s.logger)
	}

	// Invitation management (must come BEFORE /:pantryId routes to avoid conflicts)
	group.Get("/invitations", membershipHandler.GetPendingInvitations)
	group.Post("/invitations/accept", membershipHandler.AcceptInvitation)
	group.Post("/invitations/reject", membershipHandler.RejectInvitation)

	// Pantry management
	group.Post("/", pantryHandler.CreatePantry)
	group.Get("/", pantryHandler.GetPantries)
	group.Get("/:pantryId", pantryHandler.GetPantry)
	group.Put("/:pantryId", pantryHandler.UpdatePantry)
	group.Delete("/:pantryId", pantryHandler.DeletePantry)
	group.Post("/:pantryId/transfer-ownership", pantryHandler.TransferOwnership)

	// Member management
	group.Post("/:pantryId/members", membershipHandler.InviteMember)
	group.Get("/:pantryId/members", membershipHandler.GetMembers)
	group.Put("/:pantryId/members/:memberId", membershipHandler.UpdateMemberRole)
	group.Delete("/:pantryId/members/:memberId", membershipHandler.RemoveMember)
	group.Post("/:pantryId/leave", membershipHandler.LeavePantry)

	// Location management
	group.Post("/:pantryId/locations", locationHandler.CreateLocation)
	group.Get("/:pantryId/locations", locationHandler.GetLocations)
	group.Get("/:pantryId/locations/:locationId", locationHandler.GetLocation)
	group.Put("/:pantryId/locations/:locationId", locationHandler.UpdateLocation)
	group.Delete("/:pantryId/locations/:locationId", locationHandler.DeleteLocation)

	// Inventory management
	group.Post("/:pantryId/inventory", inventoryHandler.CreateInventoryItem)
	group.Get("/:pantryId/inventory", inventoryHandler.GetPantryInventory)
	group.Get("/:pantryId/inventory/filter", inventoryHandler.FilterPantryInventory)
	group.Get("/:pantryId/inventory/:itemId", inventoryHandler.GetInventoryItem)
	group.Put("/:pantryId/inventory/:itemId", inventoryHandler.UpdateInventoryItem)
	group.Post("/:pantryId/inventory/:itemId/consume", inventoryHandler.ConsumeInventoryItem)

	// Bulk inventory operations
	group.Post("/:pantryId/inventory/bulk", inventoryHandler.BulkCreateInventoryItems)
	group.Put("/inventory/bulk", inventoryHandler.BulkUpdateInventoryItems)
	group.Post("/inventory/bulk/consume", inventoryHandler.BulkConsumeInventoryItems)
	group.Delete("/inventory/bulk", inventoryHandler.BulkDeleteInventoryItems)

	// Recipe ingredient consumption
	group.Post("/:pantryId/inventory/recipe/consume", inventoryHandler.ConsumeRecipeIngredients)

	// Shopping list generation
	group.Post("/:pantryId/inventory/shopping-list", inventoryHandler.GenerateShoppingList)

	// Expiration tracking and alerts (only if handler is available)
	if expirationHandler != nil {
		group.Post("/:pantryId/expiration/track", expirationHandler.TrackExpiringItems)
		group.Post("/:pantryId/expiration/alerts", expirationHandler.ConfigureAlerts)
		group.Get("/:pantryId/expiration/alerts", expirationHandler.GetAlertConfiguration)
		// Note: Global routes are registered at the root protected level, not here
	}

	// Shopping list routes
	// Shopping list CRUD operations
	group.Post("/:pantryId/shopping-lists", shoppingListHandler.CreateShoppingList)
	group.Get("/:pantryId/shopping-lists", shoppingListHandler.GetShoppingLists)
	group.Get("/:pantryId/shopping-lists/:listId", shoppingListHandler.GetShoppingList)
	group.Put("/:pantryId/shopping-lists/:listId", shoppingListHandler.UpdateShoppingList)
	group.Delete("/:pantryId/shopping-lists/:listId", shoppingListHandler.DeleteShoppingList)
	group.Patch("/:pantryId/shopping-lists/:listId/status", shoppingListHandler.ChangeShoppingListStatus)

	// Enhanced shopping list operations
	group.Post("/:pantryId/shopping-lists/:listId/complete", shoppingListHandler.CompleteShoppingList)
	group.Put("/:pantryId/shopping-lists/:listId/store", shoppingListHandler.UpdateStoreInformation)
	group.Get("/:pantryId/purchase-history", shoppingListHandler.GetPurchaseHistory)

	// Shopping list item operations
	group.Post("/:pantryId/shopping-lists/:listId/items", shoppingListHandler.AddItemToShoppingList)
	group.Put("/:pantryId/shopping-lists/:listId/items/:itemId", shoppingListHandler.UpdateShoppingListItem)
	group.Delete("/:pantryId/shopping-lists/:listId/items/:itemId", shoppingListHandler.RemoveItemFromShoppingList)
	group.Patch("/:pantryId/shopping-lists/:listId/items/:itemId/purchased", shoppingListHandler.MarkItemAsPurchased)
	group.Delete("/:pantryId/shopping-lists/:listId/items/:itemId/purchased", shoppingListHandler.MarkItemAsNotPurchased)

	// Enhanced shopping list item operations
	group.Put("/:pantryId/shopping-lists/:listId/items/:itemId/price", shoppingListHandler.UpdateShoppingListItemPrice)

	// Bulk operations
	group.Patch("/:pantryId/shopping-lists/:listId/items/bulk-purchased", shoppingListHandler.BulkMarkItemsAsPurchased)
	group.Delete("/:pantryId/shopping-lists/:listId/items/bulk-purchased", shoppingListHandler.BulkMarkItemsAsNotPurchased)

	// Statistics
	group.Get("/:pantryId/shopping-lists/:listId/stats", shoppingListHandler.GetShoppingListStatistics)
	group.Get("/:pantryId/shopping-lists/stats", shoppingListHandler.GetPantryShoppingListStatistics)
}

// setupProductCatalogRoutes configures product catalog routes
func (s *Server) setupProductCatalogRoutes(
	group fiber.Router,
	categoryRepo domain.CategoryRepository,
	unitRepo domain.UnitOfMeasureRepository,
	productRepo domain.ProductRepository,
	variantRepo domain.ProductVariantRepository,
) {
	categoryHandler := handler.NewCategoryHandler(categoryRepo, s.logger)
	unitHandler := handler.NewUnitOfMeasureHandler(unitRepo, s.logger)

	// Category routes
	categoryGroup := group.Group("/categories")
	categoryGroup.Post("/", categoryHandler.CreateCategory)
	categoryGroup.Get("/", categoryHandler.GetCategories)
	categoryGroup.Get("/:categoryId", categoryHandler.GetCategory)
	categoryGroup.Get("/:categoryId/subcategories", categoryHandler.GetSubCategories)
	categoryGroup.Put("/:categoryId", categoryHandler.UpdateCategory)
	categoryGroup.Post("/:categoryId/move", categoryHandler.MoveCategory)
	categoryGroup.Delete("/:categoryId", categoryHandler.DeleteCategory)

	// Unit of measure routes
	unitGroup := group.Group("/units")
	unitGroup.Post("/", unitHandler.CreateUnit)
	unitGroup.Post("/derived", unitHandler.CreateDerivedUnit)
	unitGroup.Get("/", unitHandler.GetUnits)
	unitGroup.Get("/:unitId", unitHandler.GetUnit)
	unitGroup.Get("/:unitId/derived", unitHandler.GetDerivedUnits)
	unitGroup.Put("/:unitId", unitHandler.UpdateUnit)
	unitGroup.Put("/:unitId/conversion-factor", unitHandler.UpdateConversionFactor)
	unitGroup.Delete("/:unitId", unitHandler.DeleteUnit)

	// Product and variant handlers
	productHandler := handler.NewProductHandler(productRepo, categoryRepo, s.logger)
	variantHandler := handler.NewProductVariantHandler(variantRepo, productRepo, unitRepo, s.logger)

	// Product routes
	productGroup := group.Group("/products")
	productGroup.Post("/", productHandler.CreateProduct)
	productGroup.Get("/", productHandler.GetProducts)
	productGroup.Get("/:productId", productHandler.GetProduct)
	productGroup.Put("/:productId", productHandler.UpdateProduct)
	productGroup.Delete("/:productId", productHandler.DeleteProduct)

	// Product variant routes
	productGroup.Post("/:productId/variants", variantHandler.CreateProductVariant)
	productGroup.Get("/:productId/variants", variantHandler.GetProductVariants)

	// Variant routes
	variantGroup := group.Group("/variants")
	variantGroup.Get("/", variantHandler.SearchProductVariants)
	variantGroup.Get("/:variantId", variantHandler.GetProductVariant)
	variantGroup.Put("/:variantId", variantHandler.UpdateProductVariant)
	variantGroup.Put("/:variantId/barcode", variantHandler.UpdateProductVariantBarcode)
	variantGroup.Delete("/:variantId", variantHandler.DeleteProductVariant)

	// Barcode lookup route
	group.Get("/barcode/:barcode", variantHandler.GetProductVariantByBarcode)
}

// setupRecipeRoutes configures recipe routes
func (s *Server) setupRecipeRoutes(group fiber.Router, recipeUsecase *usecases.RecipeUsecase) {
	recipeHandler := handler.NewRecipeHandler(recipeUsecase, s.logger)

	// Recipe CRUD operations
	group.Post("/", recipeHandler.CreateRecipe)
	group.Get("/", recipeHandler.GetUserRecipes)
	group.Get("/public", recipeHandler.GetPublicRecipes)
	group.Get("/search", recipeHandler.SearchRecipes)
	group.Get("/:recipeId", recipeHandler.GetRecipe)
	group.Put("/:recipeId", recipeHandler.UpdateRecipe)
	group.Delete("/:recipeId", recipeHandler.DeleteRecipe)

	// Recipe operations
	group.Post("/:recipeId/scale", recipeHandler.ScaleRecipe)
	group.Post("/:recipeId/cook", recipeHandler.MarkAsCooked)
	group.Post("/:recipeId/check-inventory", recipeHandler.CheckInventoryAvailability)
}

// Helper functions

func parseDuration(s string, defaultDuration time.Duration) time.Duration {
	if s == "" {
		return defaultDuration
	}

	duration, err := time.ParseDuration(s)
	if err != nil {
		return defaultDuration
	}

	return duration
}

func parseBodyLimit(s string, defaultLimit int) int {
	if s == "" {
		return defaultLimit
	}

	// Simple parsing for common formats like "4MB", "1GB", etc.
	// For now, return default - can be enhanced later
	return defaultLimit
}

func joinStrings(slice []string, _ string) string {
	if len(slice) == 0 {
		return ""
	}

	result := slice[0]
	for i := 1; i < len(slice); i++ {
		result += "," + slice[i]
	}

	return result
}

// LoggerAdapter adapts the logger.Logger to the usecases.Logger interface
type LoggerAdapter struct {
	logger *logger.Logger
}

func (l *LoggerAdapter) Info(msg string, fields ...map[string]interface{}) {
	event := l.logger.Info()
	if len(fields) > 0 {
		for key, value := range fields[0] {
			event = event.Interface(key, value)
		}
	}
	event.Msg(msg)
}

func (l *LoggerAdapter) Error(msg string, err error, fields ...map[string]interface{}) {
	event := l.logger.Error().Err(err)
	if len(fields) > 0 {
		for key, value := range fields[0] {
			event = event.Interface(key, value)
		}
	}
	event.Msg(msg)
}

func (l *LoggerAdapter) Debug(msg string, fields ...map[string]interface{}) {
	event := l.logger.Debug()
	if len(fields) > 0 {
		for key, value := range fields[0] {
			event = event.Interface(key, value)
		}
	}
	event.Msg(msg)
}

// Debug method for services.Logger interface (single fields parameter)
func (l *LoggerAdapter) DebugSingle(msg string, fields map[string]interface{}) {
	event := l.logger.Debug()
	if fields != nil {
		for key, value := range fields {
			event = event.Interface(key, value)
		}
	}
	event.Msg(msg)
}

func (l *LoggerAdapter) LogBusinessEvent(eventType, entityID string, data map[string]interface{}) {
	l.logger.LogBusinessEvent(eventType, entityID, data)
}

// NotificationLoggerAdapter adapts the logger.Logger to the services.Logger interface
type NotificationLoggerAdapter struct {
	logger *logger.Logger
}

func (l *NotificationLoggerAdapter) Info(msg string, fields map[string]interface{}) {
	event := l.logger.Info()
	if fields != nil {
		for key, value := range fields {
			event = event.Interface(key, value)
		}
	}
	event.Msg(msg)
}

func (l *NotificationLoggerAdapter) Error(msg string, err error, fields map[string]interface{}) {
	event := l.logger.Error().Err(err)
	if fields != nil {
		for key, value := range fields {
			event = event.Interface(key, value)
		}
	}
	event.Msg(msg)
}

func (l *NotificationLoggerAdapter) Debug(msg string, fields map[string]interface{}) {
	event := l.logger.Debug()
	if fields != nil {
		for key, value := range fields {
			event = event.Interface(key, value)
		}
	}
	event.Msg(msg)
}
