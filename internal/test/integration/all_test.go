package integration

import (
	"net/http"
	"strings"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/test/testutil"
)

// AllEndpointsTestSuite tests all endpoints in a comprehensive manner
type AllEndpointsTestSuite struct {
	IntegrationTestSuite
}

// TestAllEndpoints runs comprehensive tests for all endpoints
func (s *AllEndpointsTestSuite) TestAllEndpoints() {
	s.Run("Health", s.testHealthEndpoint)
	s.Run("Authentication", s.testAuthenticationFlow)
	s.Run("UserProfile", s.testUserProfile)
	s.Run("UserAdvanced", s.testUserAdvancedFlow)
	s.Run("Categories", s.testCategoryFlow)
	s.Run("CategoriesAdvanced", s.testCategoryAdvancedFlow)
	s.Run("UnitsOfMeasure", s.testUnitOfMeasureFlow)
	s.Run("UnitsAdvanced", s.testUnitAdvancedFlow)
	s.Run("Products", s.testProductFlow)
	s.Run("ProductVariants", s.testProductVariantFlow)
	s.Run("ProductVariantsAdvanced", s.testProductVariantAdvancedFlow)
	s.Run("Pantries", s.testPantryFlow)
	s.Run("PantryLocations", s.testPantryLocationFlow)
	s.Run("Inventory", s.testInventoryFlow)
	s.Run("InventoryBulk", s.testInventoryBulkFlow)
	s.Run("Recipes", s.testRecipeFlow)
	s.Run("ShoppingLists", s.testShoppingListFlow)
	s.Run("PantryMembership", s.testPantryMembershipFlow)
	s.Run("ExpirationTracking", s.testExpirationTrackingFlow)
}

// testHealthEndpoint tests the Health Check endpoint
// Tests: Basic application health status and database connectivity
// Manual Fix Guide: If this test fails, check:
// 1. Server startup and port binding
// 2. Database connection configuration
// 3. Health check handler implementation
// 4. Basic HTTP routing setup
func (s *AllEndpointsTestSuite) testHealthEndpoint() {
	s.T().Log("Testing application health check...")
	resp, err := s.http.GET("/health")
	s.Require().NoError(err, "Health endpoint should be accessible")
	s.http.AssertStatusCode(s.T(), resp, http.StatusOK)

	var healthResp map[string]interface{}
	s.http.AssertJSONResponse(s.T(), resp, &healthResp)
	s.Equal("ok", healthResp["status"], "Health status should be 'ok'")
	s.T().Log("✅ Health check completed successfully")
}

// testAuthenticationFlow tests the Authentication System endpoints
// Tests: User registration, login, logout, and token management
// Manual Fix Guide: If this test fails, check:
// 1. User repository and database schema (users table)
// 2. Password hashing implementation (bcrypt)
// 3. JWT token generation and validation
// 4. Authentication middleware setup
// 5. Session management and token storage
func (s *AllEndpointsTestSuite) testAuthenticationFlow() {
	s.T().Log("Testing authentication flow with unique credentials...")

	// Generate unique credentials for this test
	testID := uuid.New().String()[:8]
	// Remove hyphens to make it alphanumeric only
	testID = strings.ReplaceAll(testID, "-", "")
	username := "flowtest" + testID
	email := "flowtest" + testID + "@test.com"
	password := "FlowTest123!"

	// Test 1: User Registration
	s.T().Log("Testing user registration...")
	userData := s.http.RegisterTestUser(s.T(), username, email, password)
	user, ok := userData["user"].(map[string]interface{})
	s.Require().True(ok, "Expected user field in data")
	s.NotEmpty(user["id"], "User ID should be generated")

	// Test 2: User Login
	s.T().Log("Testing user login...")
	token := s.http.LoginAndGetToken(s.T(), email, password)
	s.NotEmpty(token, "Authentication token should be returned")

	// Test 3: User Logout
	s.T().Log("Testing user logout...")
	resp, err := s.http.POST("/api/v1/auth/logout", nil)
	s.Require().NoError(err, "Logout should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ Authentication flow completed successfully")
}

// testUserProfile tests the User Profile Management endpoints
// Tests: Profile retrieval, profile updates, and user data validation
// Manual Fix Guide: If this test fails, check:
// 1. User repository GetByID method and database queries
// 2. User profile update validation and constraints
// 3. Authentication middleware and token validation
// 4. User profile handler request/response mapping
// 5. Database schema for users table and field constraints
func (s *AllEndpointsTestSuite) testUserProfile() {
	s.RequireAuth()

	// Test 1: Get User Profile
	s.T().Log("Testing user profile retrieval...")
	resp, err := s.http.GET("/api/v1/users/profile")
	s.Require().NoError(err, "Profile retrieval should succeed")
	successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	data, ok := successResp["data"].(map[string]interface{})
	s.Require().True(ok, "Response should contain data field")
	s.NotEmpty(data["id"], "User ID should be present")
	s.NotEmpty(data["username"], "Username should be present")
	s.T().Log("✅ User profile retrieved successfully")

	// Test 2: Update User Profile
	s.T().Log("Testing user profile update...")
	updateReq := map[string]interface{}{
		"first_name": "Updated",
		"last_name":  "Name",
	}

	resp, err = s.http.PUT("/api/v1/users/profile", updateReq)
	s.Require().NoError(err, "Profile update should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ User profile updated successfully")

	s.T().Log("✅ User profile flow test completed successfully")
}

// testCategoryFlow tests the Product Category Management endpoints
// Tests: Category CRUD operations and hierarchical category structure
// Manual Fix Guide: If this test fails, check:
// 1. Category repository and database schema (categories table)
// 2. Category validation rules (name uniqueness, description length)
// 3. Category handler request/response mapping
// 4. Parent-child category relationships and constraints
// 5. Category deletion cascade behavior
func (s *AllEndpointsTestSuite) testCategoryFlow() {
	s.RequireAuth()

	// Test 1: Create Category
	s.T().Log("Testing category creation...")
	categoryReq := map[string]interface{}{
		"name":        "Flow Test Category",
		"description": "Category for comprehensive flow testing",
	}

	resp, err := s.http.POST("/api/v1/catalog/categories", categoryReq)
	s.Require().NoError(err, "Category creation should succeed")
	successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	categoryID := testutil.ExtractIDFromResponse(s.T(), successResp)
	s.T().Logf("✅ Category created with ID: %s", categoryID)

	// Test 2: Get Category by ID
	s.T().Log("Testing category retrieval by ID...")
	path := testutil.BuildPath("/api/v1/catalog/categories/%s", categoryID)
	resp, err = s.http.GET(path)
	s.Require().NoError(err, "Category retrieval should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ Category retrieved successfully")

	// Test 3: List All Categories
	s.T().Log("Testing category list retrieval...")
	resp, err = s.http.GET("/api/v1/catalog/categories")
	s.Require().NoError(err, "Category list should be accessible")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ Category list retrieved successfully")

	// Test 4: Update Category
	s.T().Log("Testing category update...")
	updateReq := map[string]interface{}{
		"name":        "Updated Flow Test Category",
		"description": "Updated category description for testing",
	}

	resp, err = s.http.PUT(path, updateReq)
	s.Require().NoError(err, "Category update should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ Category updated successfully")

	// Test 5: Delete Category
	s.T().Log("Testing category deletion...")
	resp, err = s.http.DELETE(path)
	s.Require().NoError(err, "Category deletion should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ Category deleted successfully")

	s.T().Log("✅ Category flow test completed successfully")
}

func (s *AllEndpointsTestSuite) testUnitOfMeasureFlow() {
	s.RequireAuth()

	// Create unit
	unitReq := map[string]interface{}{
		"name":        "Flow Test Unit",
		"symbol":      "ftu",
		"description": "Unit for flow testing",
		"type":        "weight",
	}

	resp, err := s.http.POST("/api/v1/catalog/units", unitReq)
	s.Require().NoError(err)
	successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	unitID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Get unit
	path := testutil.BuildPath("/api/v1/catalog/units/%s", unitID)
	resp, err = s.http.GET(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// List units
	resp, err = s.http.GET("/api/v1/catalog/units")
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Update unit
	updateReq := map[string]interface{}{
		"name":        "Updated Flow Test Unit",
		"symbol":      "uftu", // Required field
		"description": "Updated unit description",
	}

	resp, err = s.http.PUT(path, updateReq)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Delete unit
	resp, err = s.http.DELETE(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
}

func (s *AllEndpointsTestSuite) testProductFlow() {
	s.RequireAuth()

	// Create category first
	categoryReq := map[string]interface{}{
		"name": "Product Flow Category",
	}

	resp, err := s.http.POST("/api/v1/catalog/categories", categoryReq)
	s.Require().NoError(err)
	successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	categoryID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Create unit first
	unitReq := map[string]interface{}{
		"name":   "Product Flow Unit",
		"symbol": "pfu",
		"type":   "weight",
	}

	resp, err = s.http.POST("/api/v1/catalog/units", unitReq)
	s.Require().NoError(err)
	successResp = s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	unitID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Create product
	productReq := map[string]interface{}{
		"name":        "Flow Test Product",
		"description": "Product for flow testing",
		"category_id": categoryID,
		"brand":       "Test Brand",
	}

	resp, err = s.http.POST("/api/v1/catalog/products", productReq)
	s.Require().NoError(err)
	successResp = s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	productID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Create product variant
	variantReq := map[string]interface{}{
		"name":                       "Flow Test Variant",
		"description":                "Variant for flow testing",
		"barcode_gtin":               "1234567890123",
		"packaging_type":             "single", // Required field
		"default_unit_of_measure_id": unitID,
	}

	path := testutil.BuildPath("/api/v1/catalog/products/%s/variants", productID)
	resp, err = s.http.POST(path, variantReq)
	s.Require().NoError(err)
	successResp = s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	variantID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Get product
	path = testutil.BuildPath("/api/v1/catalog/products/%s", productID)
	resp, err = s.http.GET(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Get variant
	path = testutil.BuildPath("/api/v1/catalog/variants/%s", variantID)
	resp, err = s.http.GET(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// List products
	resp, err = s.http.GET("/api/v1/catalog/products")
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Search variants
	resp, err = s.http.GET("/api/v1/catalog/variants?search=Flow")
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
}

func (s *AllEndpointsTestSuite) testPantryFlow() {
	s.RequireAuth()

	// Create pantry
	pantryReq := map[string]interface{}{
		"name":        "Flow Test Pantry",
		"description": "Pantry for flow testing",
	}

	resp, err := s.http.POST("/api/v1/pantries", pantryReq)
	s.Require().NoError(err)
	successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	pantryID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Create location
	locationReq := map[string]interface{}{
		"name":        "Flow Test Location",
		"description": "Location for flow testing",
	}

	path := testutil.BuildPath("/api/v1/pantries/%s/locations", pantryID)
	resp, err = s.http.POST(path, locationReq)
	s.Require().NoError(err)
	successResp = s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	locationID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Get pantry
	path = testutil.BuildPath("/api/v1/pantries/%s", pantryID)
	resp, err = s.http.GET(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Get location
	path = testutil.BuildPath("/api/v1/pantries/%s/locations/%s", pantryID, locationID)
	resp, err = s.http.GET(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// List pantries
	resp, err = s.http.GET("/api/v1/pantries")
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// List locations
	path = testutil.BuildPath("/api/v1/pantries/%s/locations", pantryID)
	resp, err = s.http.GET(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
}

// testInventoryFlow tests the Inventory Management System endpoints
// Tests: Inventory CRUD operations, consume functionality, and pantry integration
// Manual Fix Guide: If this test fails, check:
// 1. Inventory repository and database schema (inventory table)
// 2. Product variant and pantry foreign key relationships
// 3. Quantity tracking and consume operation logic
// 4. Expiration date handling and validation
// 5. Pantry location integration and constraints
func (s *AllEndpointsTestSuite) testInventoryFlow() {
	s.T().Log("Testing inventory management with full CRUD operations...")

	// Create authenticated user and get user data
	token, userData := s.CreateAuthenticatedUser()
	s.http.SetAuthToken(token)

	// Extract user ID from the authenticated user
	user, ok := userData["user"].(map[string]interface{})
	s.Require().True(ok, "Expected user object in auth response")

	userID, ok := user["id"].(string)
	s.Require().True(ok, "Expected user ID in user object")

	// Create test data using the authenticated user's ID
	testData, err := s.createTestDataForUser(userID)
	s.Require().NoError(err)

	// Create inventory item
	inventoryReq := map[string]interface{}{
		"product_variant_id": testData.Variant.ID.String(),
		"quantity":           10.0,
		"unit_of_measure_id": testData.Unit.ID.String(),
		"location_id":        testData.Location.ID.String(),
		"notes":              "Flow test inventory item",
	}

	path := testutil.BuildPath("/api/v1/pantries/%s/inventory", testData.Pantry.ID.String())
	resp, err := s.http.POST(path, inventoryReq)
	s.Require().NoError(err)

	successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	inventoryID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Get inventory item
	path = testutil.BuildPath("/api/v1/pantries/%s/inventory/%s", testData.Pantry.ID.String(), inventoryID)
	resp, err = s.http.GET(path)
	s.Require().NoError(err)

	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// List inventory
	path = testutil.BuildPath("/api/v1/pantries/%s/inventory", testData.Pantry.ID.String())
	resp, err = s.http.GET(path)
	s.Require().NoError(err)

	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Test inventory filtering
	s.T().Log("Testing inventory filtering functionality...")

	// Filter by location
	path = testutil.BuildPath("/api/v1/pantries/%s/inventory/filter?location_id=%s", testData.Pantry.ID.String(), testData.Location.ID.String())
	resp, err = s.http.GET(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Filter by category
	path = testutil.BuildPath("/api/v1/pantries/%s/inventory/filter?category_id=%s", testData.Pantry.ID.String(), testData.Category.ID.String())
	resp, err = s.http.GET(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Filter by text search
	path = testutil.BuildPath("/api/v1/pantries/%s/inventory/filter?query=Flow", testData.Pantry.ID.String())
	resp, err = s.http.GET(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Filter by stock status
	path = testutil.BuildPath("/api/v1/pantries/%s/inventory/filter?stock_status=well_stocked", testData.Pantry.ID.String())
	resp, err = s.http.GET(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Filter with multiple parameters
	path = testutil.BuildPath("/api/v1/pantries/%s/inventory/filter?location_id=%s&query=Flow&sort_by=quantity&sort_order=desc",
		testData.Pantry.ID.String(), testData.Location.ID.String())
	resp, err = s.http.GET(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Update inventory item
	updateReq := map[string]interface{}{
		"quantity": 8.0,
		"notes":    "Updated flow test inventory item",
	}

	path = testutil.BuildPath("/api/v1/pantries/%s/inventory/%s", testData.Pantry.ID.String(), inventoryID)
	resp, err = s.http.PUT(path, updateReq)
	s.Require().NoError(err)

	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Consume inventory
	consumeReq := map[string]interface{}{
		"consumed_quantity": 2.0,
		"notes":             "Consumed for flow test",
	}

	path = testutil.BuildPath("/api/v1/pantries/%s/inventory/%s/consume", testData.Pantry.ID.String(), inventoryID)
	resp, err = s.http.POST(path, consumeReq)
	s.Require().NoError(err)

	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
}

// testUserAdvancedFlow tests the Advanced User Management endpoints
// Tests: Password change functionality with security validation
// Manual Fix Guide: If this test fails, check:
// 1. ChangePassword usecase implementation and password validation
// 2. Password hashing (bcrypt) and comparison logic
// 3. Token revocation after password change
// 4. Password strength validation rules
// 5. Database transaction handling for password updates
func (s *AllEndpointsTestSuite) testUserAdvancedFlow() {
	s.RequireAuth()

	// Test 1: Change Password with Security Validation
	s.T().Log("Testing password change with security validation...")
	changePasswordReq := map[string]interface{}{
		"current_password": "TestPassword123!",
		"new_password":     "NewTestPassword123!",
		"confirm_password": "NewTestPassword123!",
	}

	resp, err := s.http.POST("/api/v1/users/change-password", changePasswordReq)
	s.Require().NoError(err, "Password change should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ Password changed successfully with proper security validation")

	s.T().Log("✅ User advanced flow test completed successfully")
}

func (s *AllEndpointsTestSuite) testCategoryAdvancedFlow() {
	s.RequireAuth()

	// Create parent category
	parentReq := map[string]interface{}{
		"name": "Parent Category",
	}

	resp, err := s.http.POST("/api/v1/catalog/categories", parentReq)
	s.Require().NoError(err)
	successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	parentID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Create child category with unique name
	childReq := map[string]interface{}{
		"name":               "Unique Child Category",
		"parent_category_id": parentID,
	}

	resp, err = s.http.POST("/api/v1/catalog/categories", childReq)
	s.Require().NoError(err)
	successResp = s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	childID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Get subcategories
	path := testutil.BuildPath("/api/v1/catalog/categories/%s/subcategories", parentID)
	resp, err = s.http.GET(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Move category
	moveReq := map[string]interface{}{
		"parent_category_id": nil, // Move to root
	}

	path = testutil.BuildPath("/api/v1/catalog/categories/%s/move", childID)
	resp, err = s.http.POST(path, moveReq)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
}

func (s *AllEndpointsTestSuite) testUnitAdvancedFlow() {
	s.RequireAuth()

	// Create base unit
	baseReq := map[string]interface{}{
		"name":   "Base Unit",
		"symbol": "bu",
		"type":   "weight",
	}

	resp, err := s.http.POST("/api/v1/catalog/units", baseReq)
	s.Require().NoError(err)
	successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	baseID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Create derived unit
	derivedReq := map[string]interface{}{
		"name":              "Derived Unit",
		"symbol":            "du",
		"type":              "weight", // Must match base unit type
		"base_unit_id":      baseID,
		"conversion_factor": 1000.0,
	}

	resp, err = s.http.POST("/api/v1/catalog/units/derived", derivedReq)
	s.Require().NoError(err)
	successResp = s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	_ = testutil.ExtractIDFromResponse(s.T(), successResp) // derivedID for future use

	// Get derived units
	path := testutil.BuildPath("/api/v1/catalog/units/%s/derived", baseID)
	resp, err = s.http.GET(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Note: Conversion factor update endpoint is disabled due to implementation complexity
	// This feature requires advanced unit conversion logic and validation
	// Future implementation should include:
	// 1. Conversion factor validation against base units
	// 2. Circular dependency detection in unit relationships
	// 3. Impact analysis on existing inventory items
	// 4. Historical conversion factor tracking for audit purposes
}

func (s *AllEndpointsTestSuite) testProductVariantFlow() {
	s.RequireAuth()

	// Create category and unit first
	categoryReq := map[string]interface{}{
		"name": "Variant Flow Category",
	}

	resp, err := s.http.POST("/api/v1/catalog/categories", categoryReq)
	s.Require().NoError(err)
	successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	categoryID := testutil.ExtractIDFromResponse(s.T(), successResp)

	unitReq := map[string]interface{}{
		"name":   "Variant Flow Unit",
		"symbol": "vfu",
		"type":   "weight",
	}

	resp, err = s.http.POST("/api/v1/catalog/units", unitReq)
	s.Require().NoError(err)
	successResp = s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	unitID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Create product
	productReq := map[string]interface{}{
		"name":        "Variant Flow Product",
		"category_id": categoryID,
	}

	resp, err = s.http.POST("/api/v1/catalog/products", productReq)
	s.Require().NoError(err)
	successResp = s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	productID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Create variant with barcode
	variantReq := map[string]interface{}{
		"name":                       "Variant Flow Test",
		"barcode_gtin":               "9876543210987",
		"packaging_type":             "single",
		"default_unit_of_measure_id": unitID,
	}

	path := testutil.BuildPath("/api/v1/catalog/products/%s/variants", productID)
	resp, err = s.http.POST(path, variantReq)
	s.Require().NoError(err)
	successResp = s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	variantID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Get variant by ID
	path = testutil.BuildPath("/api/v1/catalog/variants/%s", variantID)
	resp, err = s.http.GET(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Search variants
	resp, err = s.http.GET("/api/v1/catalog/variants?search=Variant")
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Get variant by barcode
	resp, err = s.http.GET("/api/v1/catalog/barcode/9876543210987")
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Update variant
	updateReq := map[string]interface{}{
		"name":                       "Updated Variant Flow Test",
		"default_unit_of_measure_id": unitID,
		"packaging_type":             "single",
	}

	resp, err = s.http.PUT(path, updateReq)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Update barcode
	barcodeReq := map[string]interface{}{
		"barcode_gtin": "1111111111111",
	}

	path = testutil.BuildPath("/api/v1/catalog/variants/%s/barcode", variantID)
	resp, err = s.http.PUT(path, barcodeReq)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
}

func (s *AllEndpointsTestSuite) testProductVariantAdvancedFlow() {
	s.RequireAuth()

	// Create category and unit first
	categoryReq := map[string]interface{}{
		"name": "Advanced Variant Category",
	}

	resp, err := s.http.POST("/api/v1/catalog/categories", categoryReq)
	s.Require().NoError(err)
	successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	categoryID := testutil.ExtractIDFromResponse(s.T(), successResp)

	unitReq := map[string]interface{}{
		"name":   "Advanced Variant Unit",
		"symbol": "avu",
		"type":   "weight",
	}

	resp, err = s.http.POST("/api/v1/catalog/units", unitReq)
	s.Require().NoError(err)
	successResp = s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	unitID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Create product
	productReq := map[string]interface{}{
		"name":        "Advanced Variant Product",
		"category_id": categoryID,
	}

	resp, err = s.http.POST("/api/v1/catalog/products", productReq)
	s.Require().NoError(err)
	successResp = s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	productID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Create variant
	variantReq := map[string]interface{}{
		"name":                       "Advanced Variant Test",
		"packaging_type":             "single",
		"default_unit_of_measure_id": unitID,
	}

	path := testutil.BuildPath("/api/v1/catalog/products/%s/variants", productID)
	resp, err = s.http.POST(path, variantReq)
	s.Require().NoError(err)
	successResp = s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	variantID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Get product variants
	resp, err = s.http.GET(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Delete variant
	path = testutil.BuildPath("/api/v1/catalog/variants/%s", variantID)
	resp, err = s.http.DELETE(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
}

func (s *AllEndpointsTestSuite) testPantryLocationFlow() {
	// Create authenticated user and get user data
	token, userData := s.CreateAuthenticatedUser()
	s.http.SetAuthToken(token)

	// Extract user ID from the authenticated user
	user, ok := userData["user"].(map[string]interface{})
	s.Require().True(ok, "Expected user object in auth response")

	userID, ok := user["id"].(string)
	s.Require().True(ok, "Expected user ID in user object")

	// Create test data using the authenticated user's ID
	testData, err := s.createTestDataForUser(userID)
	s.Require().NoError(err)

	// Create location
	locationReq := map[string]interface{}{
		"name":          "Test Location",
		"description":   "Location for flow testing",
		"location_type": "shelf",
	}

	path := testutil.BuildPath("/api/v1/pantries/%s/locations", testData.Pantry.ID.String())
	resp, err := s.http.POST(path, locationReq)
	s.Require().NoError(err)
	successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	locationID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Get location
	path = testutil.BuildPath("/api/v1/pantries/%s/locations/%s", testData.Pantry.ID.String(), locationID)
	resp, err = s.http.GET(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// List locations
	path = testutil.BuildPath("/api/v1/pantries/%s/locations", testData.Pantry.ID.String())
	resp, err = s.http.GET(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Update location
	updateReq := map[string]interface{}{
		"name":          "Updated Test Location",
		"description":   "Updated location for flow testing",
		"location_type": "refrigerator",
	}

	path = testutil.BuildPath("/api/v1/pantries/%s/locations/%s", testData.Pantry.ID.String(), locationID)
	resp, err = s.http.PUT(path, updateReq)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Delete location
	resp, err = s.http.DELETE(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
}

// testInventoryBulkFlow tests the Bulk Inventory Operations endpoints
// Tests: Bulk create, update, consume, and delete operations for inventory management
// Manual Fix Guide: If this test fails, check:
// 1. Bulk inventory repository methods and transaction handling
// 2. Batch processing logic and error handling for partial failures
// 3. Inventory validation rules applied to bulk operations
// 4. Database constraints and foreign key relationships in bulk inserts
// 5. Response formatting for bulk operation results and error reporting
func (s *AllEndpointsTestSuite) testInventoryBulkFlow() {
	s.T().Log("Testing bulk inventory operations with comprehensive validation...")

	// Create authenticated user and get user data
	token, userData := s.CreateAuthenticatedUser()
	s.http.SetAuthToken(token)

	// Extract user ID from the authenticated user
	user, ok := userData["user"].(map[string]interface{})
	s.Require().True(ok, "Expected user object in auth response")

	userID, ok := user["id"].(string)
	s.Require().True(ok, "Expected user ID in user object")

	// Create test data using the authenticated user's ID
	testData, err := s.createTestDataForUser(userID)
	s.Require().NoError(err, "Test data creation should succeed")

	// Test 1: Bulk Create Inventory Items
	s.T().Log("Testing bulk inventory creation with multiple items...")
	bulkCreateReq := map[string]interface{}{
		"items": []map[string]interface{}{
			{
				"product_variant_id": testData.Variant.ID.String(),
				"quantity":           5.0,
				"unit_of_measure_id": testData.Unit.ID.String(),
				"location_id":        testData.Location.ID.String(),
				"notes":              "Bulk created item 1 for testing",
			},
			{
				"product_variant_id": testData.Variant.ID.String(),
				"quantity":           3.0,
				"unit_of_measure_id": testData.Unit.ID.String(),
				"location_id":        testData.Location.ID.String(),
				"notes":              "Bulk created item 2 for testing",
			},
		},
	}

	path := testutil.BuildPath("/api/v1/pantries/%s/inventory/bulk", testData.Pantry.ID.String())
	resp, err := s.http.POST(path, bulkCreateReq)
	s.Require().NoError(err, "Bulk inventory creation should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	s.T().Log("✅ Bulk inventory items created successfully")

	s.T().Log("✅ Bulk inventory operations test completed successfully")
}

// testRecipeFlow tests the Recipe Management System endpoints
// Tests: Recipe CRUD operations, scaling, cooking tracking, and inventory checking
// Manual Fix Guide: If this test fails, check:
// 1. Recipe repository GORM preloading (ingredients, instructions, nutrition)
// 2. Recipe domain model relationships (many-to-many tags table)
// 3. Recipe handler request/response mapping
// 4. Recipe database schema and foreign key constraints
func (s *AllEndpointsTestSuite) testRecipeFlow() {
	s.RequireAuth()

	// Test 1: Create Recipe with ingredients and instructions
	// This tests the recipe creation endpoint with complex nested data
	s.T().Log("Testing recipe creation with ingredients and instructions...")
	recipeReq := map[string]interface{}{
		"title":       "Integration Test Recipe",
		"description": "A comprehensive test recipe for endpoint validation",
		"cuisine":     "Test Cuisine",
		"category":    "Test Category",
		"difficulty":  "medium",
		"prep_time":   15,
		"cook_time":   30,
		"servings":    4,
		"is_public":   false,
		"source":      "Test Suite",
		"notes":       "Created during integration testing",
		"ingredients": []map[string]interface{}{
			{
				"name":        "Test Ingredient 1",
				"quantity":    2.0,
				"unit":        "cups",
				"preparation": "diced",
				"is_optional": false,
			},
			{
				"name":        "Test Ingredient 2",
				"quantity":    1.5,
				"unit":        "tbsp",
				"preparation": "minced",
				"is_optional": true,
			},
		},
		"instructions": []map[string]interface{}{
			{
				"title":       "Prepare ingredients",
				"instruction": "Dice the first ingredient and mince the second ingredient according to preparation notes",
				"duration":    10,
				"temperature": 25,
			},
			{
				"title":       "Cook the dish",
				"instruction": "Combine all ingredients and cook for the specified time at medium heat",
				"duration":    20,
				"temperature": 180,
			},
		},
		"nutrition": map[string]interface{}{
			"calories":      250,
			"protein":       15.5,
			"carbohydrates": 30.0,
			"fat":           8.0,
			"serving_size":  "1 portion",
		},
	}

	resp, err := s.http.POST("/api/v1/recipes", recipeReq)
	s.Require().NoError(err, "Recipe creation should succeed")
	successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	recipeID := testutil.ExtractIDFromResponse(s.T(), successResp)
	s.T().Logf("Created recipe with ID: %s", recipeID)

	// Test 2: Get Recipe by ID
	// This tests recipe retrieval with all nested relationships
	s.T().Log("Testing recipe retrieval by ID...")
	path := testutil.BuildPath("/api/v1/recipes/%s", recipeID)
	resp, err = s.http.GET(path)
	s.Require().NoError(err, "Recipe retrieval by ID should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Test 3: Get User Recipes List
	// This tests the user's recipe collection endpoint
	s.T().Log("Testing user recipes list retrieval...")
	resp, err = s.http.GET("/api/v1/recipes")
	s.Require().NoError(err, "User recipes list should be accessible")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Test 4: Get Public Recipes
	// This tests the public recipe discovery endpoint
	s.T().Log("Testing public recipes list retrieval...")
	resp, err = s.http.GET("/api/v1/recipes/public")
	s.Require().NoError(err, "Public recipes should be accessible")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Test 5: Search Recipes
	// This tests the recipe search functionality
	s.T().Log("Testing recipe search functionality...")
	resp, err = s.http.GET("/api/v1/recipes/search?q=Integration")
	s.Require().NoError(err, "Recipe search should work")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Test 6: Update Recipe
	// This tests recipe modification capabilities
	s.T().Log("Testing recipe update...")
	updateReq := map[string]interface{}{
		"title":       "Updated Integration Test Recipe",
		"description": "Updated description for testing",
		"servings":    6,
		"is_public":   true,
	}
	resp, err = s.http.PUT(path, updateReq)
	s.Require().NoError(err, "Recipe update should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Test 7: Scale Recipe
	// This tests recipe scaling functionality for different serving sizes
	s.T().Log("Testing recipe scaling...")
	scaleReq := map[string]interface{}{
		"servings": 8,
	}
	scalePath := testutil.BuildPath("/api/v1/recipes/%s/scale", recipeID)
	resp, err = s.http.POST(scalePath, scaleReq)
	s.Require().NoError(err, "Recipe scaling should work")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Test 8: Mark Recipe as Cooked
	// This tests cooking tracking functionality
	s.T().Log("Testing mark recipe as cooked...")
	cookPath := testutil.BuildPath("/api/v1/recipes/%s/cook", recipeID)
	resp, err = s.http.POST(cookPath, nil)
	s.Require().NoError(err, "Marking recipe as cooked should work")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Test 9: Check Inventory Availability
	// This tests recipe-to-inventory integration
	s.T().Log("Testing inventory availability check...")
	checkReq := map[string]interface{}{
		"pantry_id": nil, // Check across all pantries
	}
	checkPath := testutil.BuildPath("/api/v1/recipes/%s/check-inventory", recipeID)
	resp, err = s.http.POST(checkPath, checkReq)
	s.Require().NoError(err, "Inventory availability check should work")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Test 10: Delete Recipe
	// This tests recipe deletion and cleanup
	s.T().Log("Testing recipe deletion...")
	resp, err = s.http.DELETE(path)
	s.Require().NoError(err, "Recipe deletion should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Test 11: Verify Recipe is Deleted (Expected 404 Error)
	// This confirms the recipe is properly removed - the error log below is EXPECTED
	s.T().Log("Verifying recipe deletion (expecting 404 NOT_FOUND - this error is normal)...")
	resp, err = s.http.GET(path)
	s.Require().NoError(err, "GET request after deletion should not error")
	s.http.AssertErrorResponse(s.T(), resp, http.StatusNotFound)
	s.T().Log("✅ Recipe deletion verified - 404 error above is expected and correct")

	s.T().Log("✅ Recipe flow test completed successfully")
}

// testPantryMembershipFlow tests the Pantry Membership Management System endpoints
// Tests: Member invitation, acceptance, role management, and removal operations
// Manual Fix Guide: If this test fails, check:
// 1. Pantry membership repository and database schema (pantry_memberships table)
// 2. User authentication and context extraction (getUserIDFromContext function)
// 3. Pantry authorization service and permission checking logic
// 4. Email-based user lookup and invitation validation
// 5. Membership status transitions and business rule enforcement
func (s *AllEndpointsTestSuite) testPantryMembershipFlow() {
	s.T().Log("Testing pantry membership management with comprehensive operations...")

	// Create authenticated user and get user data
	token, userData := s.CreateAuthenticatedUser()
	s.http.SetAuthToken(token)

	// Extract user ID from the authenticated user
	user, ok := userData["user"].(map[string]interface{})
	s.Require().True(ok, "Expected user object in auth response")

	userID, ok := user["id"].(string)
	s.Require().True(ok, "Expected user ID in user object")

	// Create test data using the authenticated user's ID
	testData, err := s.createTestDataForUser(userID)
	s.Require().NoError(err, "Test data creation should succeed")

	// Test 1: Create Another User for Membership Testing
	s.T().Log("Creating a second user for membership invitation testing...")
	testID := uuid.New().String()[:8]
	testID = strings.ReplaceAll(testID, "-", "")
	memberUsername := "member" + testID
	memberEmail := "member" + testID + "@test.com"
	memberPassword := "MemberTest123!"

	s.http.RegisterTestUser(s.T(), memberUsername, memberEmail, memberPassword)
	s.T().Logf("✅ Second user created: %s (%s)", memberUsername, memberEmail)

	// Test 2: Invite Member to Pantry
	s.T().Log("Testing member invitation to pantry...")
	inviteReq := map[string]interface{}{
		"email": memberEmail,
		"role":  "editor", // Use valid role
	}

	path := testutil.BuildPath("/api/v1/pantries/%s/members", testData.Pantry.ID.String())
	resp, err := s.http.POST(path, inviteReq)
	s.Require().NoError(err, "Member invitation should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	s.T().Log("✅ Member invitation sent successfully")

	// Test 3: Get Pantry Members (as owner)
	s.T().Log("Testing pantry members list retrieval...")
	resp, err = s.http.GET(path)
	s.Require().NoError(err, "Pantry members list should be accessible")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ Pantry members list retrieved successfully")

	// Test 4: Switch to Invited User and Get Pending Invitations
	s.T().Log("Switching to invited user and testing pending invitations retrieval...")
	memberToken := s.http.LoginAndGetToken(s.T(), memberEmail, memberPassword)
	s.http.SetAuthToken(memberToken)
	s.T().Logf("✅ Switched to invited user: %s", memberEmail)

	s.T().Log("Testing pending invitations retrieval...")
	resp, err = s.http.GET("/api/v1/pantries/invitations")
	s.Require().NoError(err, "Pending invitations request should not error")

	// Debug: Print the invitations response to understand the error
	s.T().Log("📋 Pending invitations response:")
	s.http.PrintResponse(s.T(), resp)

	invitationsResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Extract membership ID from invitations
	data, ok := invitationsResp["data"].([]interface{})
	s.Require().True(ok, "Expected data array in invitations response")
	s.Require().Greater(len(data), 0, "Expected at least one invitation")

	invitation, ok := data[0].(map[string]interface{})
	s.Require().True(ok, "Expected invitation object")
	membershipID, ok := invitation["id"].(string)
	s.Require().True(ok, "Expected membership ID in invitation")

	// Accept invitation
	acceptReq := map[string]interface{}{
		"membership_id": membershipID,
	}

	resp, err = s.http.POST("/api/v1/pantries/invitations/accept", acceptReq)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Switch back to owner
	s.http.SetAuthToken(token)

	// Update member role (use membership ID, not user ID)
	updateRoleReq := map[string]interface{}{
		"role": "admin",
	}

	path = testutil.BuildPath("/api/v1/pantries/%s/members/%s", testData.Pantry.ID.String(), membershipID)
	resp, err = s.http.PUT(path, updateRoleReq)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
}

// testShoppingListFlow tests the Shopping List Management System endpoints
// Tests: Shopping list CRUD operations, item management, status changes, and bulk operations
// Manual Fix Guide: If this test fails, check:
// 1. Shopping list repository and database schema (shopping_lists, shopping_list_items tables)
// 2. Shopping list service authorization and pantry access validation
// 3. Shopping list handler request/response mapping and validation
// 4. Shopping list item relationships (product variants, units of measure)
// 5. Shopping list status management and filtering logic
func (s *AllEndpointsTestSuite) testShoppingListFlow() {
	s.T().Log("Testing shopping list management with comprehensive CRUD operations...")

	// Create authenticated user and get user data
	token, userData := s.CreateAuthenticatedUser()
	s.http.SetAuthToken(token)

	// Extract user ID from the authenticated user
	user, ok := userData["user"].(map[string]interface{})
	s.Require().True(ok, "Expected user object in auth response")

	userID, ok := user["id"].(string)
	s.Require().True(ok, "Expected user ID in user object")

	// Create test data using the authenticated user's ID
	testData, err := s.createTestDataForUser(userID)
	s.Require().NoError(err, "Test data creation should succeed")

	// Test 1: Create Shopping List
	s.T().Log("Testing shopping list creation...")
	listReq := map[string]interface{}{
		"name":        "Weekly Groceries Test",
		"description": "Shopping list for comprehensive endpoint testing",
	}

	listPath := testutil.BuildPath("/api/v1/pantries/%s/shopping-lists", testData.Pantry.ID.String())
	resp, err := s.http.POST(listPath, listReq)
	s.Require().NoError(err, "Shopping list creation should succeed")
	successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	listID := testutil.ExtractIDFromResponse(s.T(), successResp)
	s.T().Logf("✅ Shopping list created with ID: %s", listID)

	// Test 2: Get Shopping List by ID
	s.T().Log("Testing shopping list retrieval by ID...")
	getListPath := testutil.BuildPath("/api/v1/pantries/%s/shopping-lists/%s", testData.Pantry.ID.String(), listID)
	resp, err = s.http.GET(getListPath)
	s.Require().NoError(err, "Shopping list retrieval should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ Shopping list retrieved successfully")

	// Test 3: List Shopping Lists for Pantry
	s.T().Log("Testing shopping lists list retrieval...")
	resp, err = s.http.GET(listPath)
	s.Require().NoError(err, "Shopping lists list should be accessible")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ Shopping lists list retrieved successfully")

	// Test 4: Add Item to Shopping List (with product variant)
	s.T().Log("Testing shopping list item addition with product variant...")
	itemReq := map[string]interface{}{
		"product_variant_id": testData.Variant.ID.String(),
		"quantity_desired":   2.0,
		"unit_of_measure_id": testData.Unit.ID.String(),
		"notes":              "Product variant item for testing",
	}

	itemsPath := testutil.BuildPath("/api/v1/pantries/%s/shopping-lists/%s/items", testData.Pantry.ID.String(), listID)
	resp, err = s.http.POST(itemsPath, itemReq)
	s.Require().NoError(err, "Shopping list item addition should succeed")
	successResp = s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	itemID := testutil.ExtractIDFromResponse(s.T(), successResp)
	s.T().Logf("✅ Shopping list item added with ID: %s", itemID)

	// Test 5: Add Free Text Item to Shopping List
	s.T().Log("Testing shopping list free text item addition...")
	freeTextItemReq := map[string]interface{}{
		"free_text_name":   "Organic Bananas",
		"quantity_desired": 6.0,
		"notes":            "Free text item for testing",
	}

	resp, err = s.http.POST(itemsPath, freeTextItemReq)
	s.Require().NoError(err, "Free text item addition should succeed")
	successResp = s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	freeTextItemID := testutil.ExtractIDFromResponse(s.T(), successResp)
	s.T().Logf("✅ Free text shopping list item added with ID: %s", freeTextItemID)

	// Test 6: Update Shopping List Item
	s.T().Log("Testing shopping list item update...")
	updateItemReq := map[string]interface{}{
		"product_variant_id": testData.Variant.ID.String(), // Keep the original product variant
		"quantity_desired":   3.0,
		"notes":              "Updated quantity for testing",
	}

	updateItemPath := testutil.BuildPath("/api/v1/pantries/%s/shopping-lists/%s/items/%s", testData.Pantry.ID.String(), listID, itemID)
	resp, err = s.http.PUT(updateItemPath, updateItemReq)
	s.Require().NoError(err, "Shopping list item update should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ Shopping list item updated successfully")

	// Test 7: Mark Item as Purchased
	s.T().Log("Testing mark item as purchased...")
	markPurchasedPath := testutil.BuildPath("/api/v1/pantries/%s/shopping-lists/%s/items/%s/purchased", testData.Pantry.ID.String(), listID, itemID)
	resp, err = s.http.PATCH(markPurchasedPath, nil)
	s.Require().NoError(err, "Mark item as purchased should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ Item marked as purchased successfully")

	// Test 8: Get Shopping List Statistics
	s.T().Log("Testing shopping list statistics retrieval...")
	statsPath := testutil.BuildPath("/api/v1/pantries/%s/shopping-lists/%s/stats", testData.Pantry.ID.String(), listID)
	resp, err = s.http.GET(statsPath)
	s.Require().NoError(err, "Shopping list statistics should be accessible")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ Shopping list statistics retrieved successfully")

	// Test 9: Change Shopping List Status
	s.T().Log("Testing shopping list status change...")
	statusReq := map[string]interface{}{
		"status": "completed",
	}

	statusPath := testutil.BuildPath("/api/v1/pantries/%s/shopping-lists/%s/status", testData.Pantry.ID.String(), listID)
	resp, err = s.http.PATCH(statusPath, statusReq)
	s.Require().NoError(err, "Shopping list status change should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ Shopping list status changed successfully")

	// Test 10: Update Shopping List
	s.T().Log("Testing shopping list update...")
	updateListReq := map[string]interface{}{
		"name":        "Updated Weekly Groceries Test",
		"description": "Updated shopping list for comprehensive testing",
	}

	resp, err = s.http.PUT(getListPath, updateListReq)
	s.Require().NoError(err, "Shopping list update should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ Shopping list updated successfully")

	// Test 11: Delete Shopping List Item
	s.T().Log("Testing shopping list item deletion...")
	resp, err = s.http.DELETE(updateItemPath)
	s.Require().NoError(err, "Shopping list item deletion should succeed")
	s.http.AssertStatusCode(s.T(), resp, http.StatusNoContent)
	s.T().Log("✅ Shopping list item deleted successfully")

	// Test 12: Delete Shopping List
	s.T().Log("Testing shopping list deletion...")
	resp, err = s.http.DELETE(getListPath)
	s.Require().NoError(err, "Shopping list deletion should succeed")
	s.http.AssertStatusCode(s.T(), resp, http.StatusNoContent)
	s.T().Log("✅ Shopping list deleted successfully")

	// Test 13: Verify Shopping List is Deleted (Expected 404 Error)
	s.T().Log("Verifying shopping list deletion (expecting 404 NOT_FOUND - this error is normal)...")
	resp, err = s.http.GET(getListPath)
	s.Require().NoError(err, "GET request after deletion should not error")
	s.http.AssertErrorResponse(s.T(), resp, http.StatusNotFound)
	s.T().Log("✅ Shopping list deletion verified - 404 error above is expected and correct")

	s.T().Log("✅ Shopping list flow test completed successfully")

	// Test enhanced shopping list features
	s.testEnhancedShoppingListFeatures()
}

func (s *AllEndpointsTestSuite) testEnhancedShoppingListFeatures() {
	s.T().Log("Testing enhanced shopping list features...")

	// Create test data
	testData, err := s.CreateTestData()
	s.Require().NoError(err, "Test data creation should succeed")

	// Create a new shopping list for enhanced features testing
	createReq := map[string]interface{}{
		"name":        "Enhanced Features Test List",
		"description": "Testing enhanced shopping list features",
	}

	listPath := testutil.BuildPath("/api/v1/pantries/%s/shopping-lists", testData.Pantry.ID.String())
	resp, err := s.http.POST(listPath, createReq)
	s.Require().NoError(err, "Shopping list creation should succeed")
	successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	listID := testutil.ExtractIDFromResponse(s.T(), successResp)
	s.T().Logf("✅ Enhanced features test shopping list created with ID: %s", listID)

	// Test store information update
	s.T().Log("Testing store information update...")
	storeReq := map[string]interface{}{
		"store_name":    "Supermarket ABC",
		"store_address": "123 Main Street, City, State 12345",
		"store_contact": "+1-555-123-4567",
	}

	storePath := testutil.BuildPath("/api/v1/pantries/%s/shopping-lists/%s/store", testData.Pantry.ID.String(), listID)
	resp, err = s.http.PUT(storePath, storeReq)
	s.Require().NoError(err, "Store information update should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ Store information updated successfully")

	// Add an item to test price tracking
	s.T().Log("Testing shopping list item with price information...")
	itemReq := map[string]interface{}{
		"product_variant_id": testData.Variant.ID.String(),
		"quantity_desired":   2.0,
		"unit_of_measure_id": testData.Unit.ID.String(),
		"notes":              "Test item for price tracking",
	}

	itemsPath := testutil.BuildPath("/api/v1/pantries/%s/shopping-lists/%s/items", testData.Pantry.ID.String(), listID)
	resp, err = s.http.POST(itemsPath, itemReq)
	s.Require().NoError(err, "Shopping list item addition should succeed")
	successResp = s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	itemID := testutil.ExtractIDFromResponse(s.T(), successResp)
	s.T().Logf("✅ Shopping list item added with ID: %s", itemID)

	// Test price information update
	s.T().Log("Testing item price information update...")
	priceReq := map[string]interface{}{
		"estimated_price": 5.99,
		"actual_price":    6.49,
		"price_per_unit":  3.25,
	}

	pricePath := testutil.BuildPath("/api/v1/pantries/%s/shopping-lists/%s/items/%s/price", testData.Pantry.ID.String(), listID, itemID)
	resp, err = s.http.PUT(pricePath, priceReq)
	s.Require().NoError(err, "Item price information update should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ Item price information updated successfully")

	// Test marking item as purchased with price
	s.T().Log("Testing mark item as purchased with price...")
	purchaseReq := map[string]interface{}{
		"is_purchased":   true,
		"actual_price":   6.49,
		"price_per_unit": 3.25,
	}

	purchasePath := testutil.BuildPath("/api/v1/pantries/%s/shopping-lists/%s/items/%s/purchased", testData.Pantry.ID.String(), listID, itemID)
	resp, err = s.http.PATCH(purchasePath, purchaseReq)
	s.Require().NoError(err, "Mark item as purchased should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ Item marked as purchased with price successfully")

	// Test shopping list completion
	s.T().Log("Testing shopping list completion...")
	completeReq := map[string]interface{}{
		"store_name":        "Supermarket ABC",
		"store_address":     "123 Main Street, City, State 12345",
		"store_contact":     "+1-555-123-4567",
		"receipt_image_url": "https://example.com/receipts/receipt123.jpg",
		"total_amount":      6.49,
	}

	completePath := testutil.BuildPath("/api/v1/pantries/%s/shopping-lists/%s/complete", testData.Pantry.ID.String(), listID)
	resp, err = s.http.POST(completePath, completeReq)
	s.Require().NoError(err, "Shopping list completion should succeed")
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ Shopping list completed successfully")

	// Test purchase history retrieval
	s.T().Log("Testing purchase history retrieval...")
	historyPath := testutil.BuildPath("/api/v1/pantries/%s/purchase-history", testData.Pantry.ID.String())
	resp, err = s.http.GET(historyPath)
	s.Require().NoError(err, "Purchase history retrieval should succeed")
	successResp = s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	historyData := successResp.Data.([]interface{})
	s.Require().GreaterOrEqual(len(historyData), 1, "Should have at least one completed shopping list in history")
	s.T().Logf("✅ Purchase history retrieved successfully with %d completed lists", len(historyData))

	// Verify the completed shopping list has enhanced fields
	completedList := historyData[0].(map[string]interface{})
	s.Require().Equal("completed", completedList["status"])
	s.Require().NotNil(completedList["store_name"])
	s.Require().NotNil(completedList["total_amount"])
	s.Require().NotNil(completedList["completed_at"])
	s.T().Log("✅ Completed shopping list has all enhanced fields")

	s.T().Log("✅ Enhanced shopping list features test completed successfully")
}

// testExpirationTrackingFlow tests the Expiration Tracking System endpoints
// Tests: Item expiration monitoring, alert configuration, and notification management
// Manual Fix Guide: If this test fails, check:
// 1. Alert configuration repository and JSON serialization for channels/filters
// 2. Expiration tracking use case and business logic implementation
// 3. Database schema for alert_configurations table (JSONB fields)
// 4. Notification channel validation and enum values
// 5. Date format handling for expiration dates (YYYY-MM-DD format)
func (s *AllEndpointsTestSuite) testExpirationTrackingFlow() {
	s.T().Log("Testing expiration tracking system with comprehensive alert management...")

	// Create authenticated user and get user data
	token, userData := s.CreateAuthenticatedUser()
	s.http.SetAuthToken(token)

	// Extract user ID from the authenticated user
	user, ok := userData["user"].(map[string]interface{})
	s.Require().True(ok, "Expected user object in auth response")

	userID, ok := user["id"].(string)
	s.Require().True(ok, "Expected user ID in user object")

	// Create test data using the authenticated user's ID
	testData, err := s.createTestDataForUser(userID)
	s.Require().NoError(err)

	// Create inventory item with expiration date
	inventoryReq := map[string]interface{}{
		"product_variant_id": testData.Variant.ID.String(),
		"quantity":           10.0,
		"unit_of_measure_id": testData.Unit.ID.String(),
		"location_id":        testData.Location.ID.String(),
		"expiration_date":    "2025-12-31",
		"notes":              "Expiration test inventory item",
	}

	path := testutil.BuildPath("/api/v1/pantries/%s/inventory", testData.Pantry.ID.String())
	resp, err := s.http.POST(path, inventoryReq)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)

	// Track expiring items
	trackReq := map[string]interface{}{
		"warning_days":  30,
		"alert_days":    7,
		"critical_days": 1,
		"send_alerts":   false, // Don't send alerts during test
	}

	path = testutil.BuildPath("/api/v1/pantries/%s/expiration/track", testData.Pantry.ID.String())
	resp, err = s.http.POST(path, trackReq)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Configure alerts for the pantry
	alertReq := map[string]interface{}{
		"enabled":       true,
		"warning_days":  30,
		"alert_days":    7,
		"critical_days": 1,
		"channels":      []string{"log"},
		"quiet_hours": map[string]interface{}{
			"enabled":    true,
			"start_time": "22:00",
			"end_time":   "08:00",
			"timezone":   "UTC",
		},
	}

	path = testutil.BuildPath("/api/v1/pantries/%s/expiration/alerts", testData.Pantry.ID.String())
	resp, err = s.http.POST(path, alertReq)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Get alert configuration
	resp, err = s.http.GET(path)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Configure global alerts
	globalAlertReq := map[string]interface{}{
		"enabled":       true,
		"warning_days":  14,
		"alert_days":    3,
		"critical_days": 1,
		"channels":      []string{"log"},
	}

	resp, err = s.http.POST("/api/v1/expiration/alerts/global", globalAlertReq)
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

	// Get global alert configuration
	resp, err = s.http.GET("/api/v1/expiration/alerts/global")
	s.Require().NoError(err)
	s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	s.T().Log("✅ Global alert configuration retrieved successfully")

	s.T().Log("✅ Expiration tracking flow test completed successfully")
}

// createTestDataForUser creates test data using a specific user ID
func (s *AllEndpointsTestSuite) createTestDataForUser(userIDStr string) (*testutil.TestDataSet, error) {
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, err
	}

	// Create category
	category, err := s.seeds.CreateTestCategory("", nil)
	if err != nil {
		return nil, err
	}

	// Create unit of measure
	unit, err := s.seeds.CreateTestUnitOfMeasure("Kilogram", "kg", domain.UnitTypeWeight)
	if err != nil {
		return nil, err
	}

	// Create product
	product, err := s.seeds.CreateTestProduct("", category.ID)
	if err != nil {
		return nil, err
	}

	// Create product variant
	variant, err := s.seeds.CreateTestProductVariant("", product.ID, unit.ID)
	if err != nil {
		return nil, err
	}

	// Create pantry with the authenticated user as owner
	pantry, err := s.seeds.CreateTestPantry("", userID)
	if err != nil {
		return nil, err
	}

	// Create pantry location
	location, err := s.seeds.CreateTestPantryLocation("", pantry.ID)
	if err != nil {
		return nil, err
	}

	// Create inventory item
	inventory, err := s.seeds.CreateTestInventoryItem(pantry.ID, variant.ID, unit.ID, 5.0)
	if err != nil {
		return nil, err
	}

	// Create recipe
	recipe, err := s.seeds.CreateTestRecipe("", userID)
	if err != nil {
		return nil, err
	}

	return &testutil.TestDataSet{
		User:      &domain.User{ID: userID}, // Create minimal user object
		Category:  category,
		Unit:      unit,
		Product:   product,
		Variant:   variant,
		Pantry:    pantry,
		Location:  location,
		Inventory: inventory,
		Recipe:    recipe,
	}, nil
}

// TestAllEndpointsTestSuite runs the comprehensive endpoint test suite
func TestAllEndpointsTestSuite(t *testing.T) {
	suite.Run(t, new(AllEndpointsTestSuite))
}
